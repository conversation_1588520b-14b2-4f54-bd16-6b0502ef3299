
Git Smart Protocol Advertisement Formats (Detailed)

This comment documents the expected response body structure for the  `git-upload-pack` (fetch/clone) and `git-receive-pack` (push) services
under both version 1 and version 2 of the Git Smart Protocol.

-------------------------------
VERSION 1 (<PERSON><PERSON><PERSON>, Older Clients)
-------------------------------

General Response Format (for both upload-pack and receive-pack):
    Content-Type:
      - upload-pack:    application/x-git-upload-pack-advertisement
      - receive-pack:   application/x-git-receive-pack-advertisement

    Body:
      - pkt-line: "# service=git-<service>"
      - pkt-line: flush (0000)
      - pkt-line(s): "<oid> <refname>[\\0<capabilities>]"
      - pkt-line: flush (0000)

    Notes:
      - Capabilities are sent on the first reference only, after a NUL byte.
      - All lines are packet-line encoded (each line prefixed with 4-byte hex length).

Example (upload-pack):
    001e# service=git-upload-pack
    0000
    003f5ca1c6... refs/heads/main\0multi_ack thin-pack side-band-64k ...
    003f1a2b3c... refs/tags/v1.0
    0000

Common Capabilities:
    upload-pack:
      - multi_ack
      - multi_ack_detailed
      - thin-pack
      - side-band
      - side-band-64k
      - ofs-delta
      - shallow
      - deepen-since
      - deepen-not
      - deepen-relative

    receive-pack:
      - report-status
      - delete-refs
      - side-band-64k
      - ofs-delta
      - push-options

-------------------------------
VERSION 2 (Modern, Opt-in)
-------------------------------

Activation:
    - Requires HTTP header: Git-Protocol: version=2

General Response Format:
    Content-Type: same as v1

    Body:
      - pkt-line: "# service=git-<service>"
      - pkt-line: flush (0000)
      - pkt-line: "version 2"
      - pkt-line(s): capabilities (one per line)
      - pkt-line: flush (0000)

    Notes:
      - Refs are not included here. Instead, the client will issue a subsequent `ls-refs` command to query refs explicitly.
      - This design enables a stateless and extensible protocol.

Example (upload-pack):
    001e# service=git-upload-pack
    0000
    0010version 2
    0019ls-refs=peel symrefs
    0025fetch=shallow filter ref-in-want
    0000

-------------------------------
Summary of Key Differences
-------------------------------

    Feature                 | v1                          | v2
    ------------------------|------------------------------|------------------------------
    Activation              | Default                     | Requires `Git-Protocol` header
    Version Line            | None                        | "version 2"
    Refs Advertised         | Inline in /info/refs        | Queried via `ls-refs`
    Capabilities            | On first ref (after \0)     | As individual pkt-lines
    Extensibility           | Difficult                   | Easy via additional commands
    Statelessness           | Mostly stateless            | Fully stateless

-------------------------------
Reference
-------------------------------
- Git Protocol v2 Spec: https://git-scm.com/docs/protocol-v2
- Git Packet-Line Format: https://git-scm.com/docs/technical/protocol-common
