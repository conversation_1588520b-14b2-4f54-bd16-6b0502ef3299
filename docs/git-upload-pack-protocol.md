# Git Upload-Pack Protocol Implementation

This document describes the implementation of the Git upload-pack protocol in the Axum Git Server, which handles fetch, clone, and ls-remote operations.

## Overview

The upload-pack protocol is used by Git clients to:
- **Clone repositories** (`git clone`)
- **Fetch updates** (`git fetch`, `git pull`)
- **List remote references** (`git ls-remote`)
- **Check repository status** without downloading content

Our implementation supports both Git protocol v1 (legacy) and v2 (modern) with native Rust implementations and optional git binary fallback.

## Protocol Versions

### Git Protocol v1 (Legacy)
- Traditional Git protocol with capability negotiation
- Uses packet-line format for communication
- Supports want/have negotiation for efficient transfers
- Delegates complex operations to git binary for reliability

### Git Protocol v2 (Modern)
- Command-based structure with explicit commands
- Supports `ls-refs` and `fetch` commands
- More efficient and extensible than v1
- Native Rust implementation for `ls-refs`, git binary for `fetch`

## Implementation Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Git Client    │───▶│  Upload-Pack     │───▶│  Implementation │
│ (clone/fetch)   │    │    Handler       │    │     Choice      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Protocol        │    │ • Native Rust   │
                       │ Detection       │    │ • Git Binary    │
                       │ (v1 vs v2)      │    │   Fallback      │
                       └─────────────────┘    └─────────────────┘
```

## Native Implementation Details

### Protocol v2 ls-refs Command

The `ls-refs` command lists repository references and is implemented natively in Rust:

**Request Format:**
```
0014command=ls-refs
0014agent=git/2.43.0
0001000csymrefs
0009unborn
0000
```

**Response Format:**
```
0050cacc495bf4f253af32e88e030e336981e83b7e0e HEAD symref-target:refs/heads/main
003dcacc495bf4f253af32e88e030e336981e83b7e0e refs/heads/main
0000
```

**Features Supported:**
- **HEAD reference**: Always included with commit SHA
- **Symbolic references**: Shows `symref-target` for HEAD
- **Branch references**: All `refs/heads/*` branches
- **Tag references**: All `refs/tags/*` tags
- **Empty repositories**: Returns `unborn HEAD` for repositories without commits

### Protocol v1 Implementation

Protocol v1 requests are handled by delegating to the git binary with proper environment setup:

```rust
let git_args = vec![
    "-c", "protocol.version=2",
    "upload-pack",
    "--stateless-rpc",
    "."
];
```

## Configuration Options

### Native Implementation (Default)

```bash
# Uses native Rust implementation
cargo run
```

**Advantages:**
- Better performance for ls-refs operations
- More control over response format
- Detailed logging and error handling
- No external git binary dependency for basic operations

### Git Binary Fallback

```bash
# Uses git binary for all operations
GIT_UPLOAD_PACK_FALLBACK=true cargo run
```

**Advantages:**
- Maximum compatibility with Git ecosystem
- Handles complex protocol edge cases
- Supports all Git features out of the box
- Useful for debugging protocol issues

## Request Flow

### 1. Protocol Detection
```rust
let protocol_version = match GitProtocolVersion::from_str(
    headers.get("git-protocol")
        .and_then(|h| h.to_str().ok())
        .unwrap_or(""),
) {
    Ok(result) => result,
    Err(_) => GitProtocolVersion::V1, // Default to v1
};
```

### 2. Implementation Selection
```rust
let use_fallback = std::env::var("GIT_UPLOAD_PACK_FALLBACK")
    .map(|v| v == "true" || v == "1")
    .unwrap_or(false);

if use_fallback {
    handle_upload_pack_fallback(&repo_path, &body, response_headers).await
} else if protocol_version == GitProtocolVersion::V2 {
    handle_upload_pack_native_v2(&repo_path, &body, response_headers).await
} else {
    handle_upload_pack_native_v1(&repo_path, &body, response_headers).await
}
```

### 3. Response Generation
All implementations return standardized HTTP responses with appropriate headers:

```rust
let mut response_headers = HeaderMap::new();
response_headers.insert(
    header::CONTENT_TYPE,
    "application/x-git-upload-pack-result".parse().unwrap(),
);
response_headers.insert("Cache-Control", "no-cache".parse().unwrap());
response_headers.insert("Pragma", "no-cache".parse().unwrap());
response_headers.insert(
    "Git-Protocol",
    format!("version={}", if protocol_version == GitProtocolVersion::V2 { "2" } else { "1" })
        .parse().unwrap(),
);
```

## Error Handling

### Repository Not Found
```http
HTTP/1.1 404 Not Found
Content-Type: text/plain

Repository not found
```

### Protocol Errors
```http
HTTP/1.1 500 Internal Server Error
Content-Type: text/plain

Upload-pack failed: <error details>
```

### Invalid Requests
```http
HTTP/1.1 400 Bad Request
Content-Type: text/plain

Invalid git protocol version
```

## Logging

The implementation provides detailed logging for monitoring and debugging:

```
INFO Upload-pack request - Owner: user - Repo: project.git, user_agent: git/2.43.0, protocol: version=2
INFO Using native Git upload-pack protocol v2 implementation
INFO Native v2 upload-pack completed successfully
```

**Log Levels:**
- **INFO**: Request details, implementation choice, completion status
- **DEBUG**: Git command execution details
- **ERROR**: Protocol errors, git command failures

## Performance Characteristics

### Native Implementation
- **ls-refs latency**: ~1-5ms for typical repositories
- **Memory usage**: Minimal, processes refs incrementally
- **CPU usage**: Low, efficient Rust implementation

### Git Binary Fallback
- **Latency**: ~10-50ms (includes process spawn overhead)
- **Memory usage**: Higher due to git process
- **CPU usage**: Depends on git binary efficiency

## Testing

The implementation includes comprehensive tests covering:

- **Protocol versions**: Both v1 and v2
- **Repository states**: Empty, single branch, multiple branches, with tags
- **Error conditions**: Non-existent repositories, invalid requests
- **Concurrent access**: Multiple simultaneous requests
- **Client compatibility**: Various Git client versions and user agents

### Running Tests

```bash
# Test native implementation
cargo test --test git_upload_pack_test

# Test with fallback enabled
GIT_UPLOAD_PACK_FALLBACK=true cargo test test_upload_pack_request_v1 --test git_upload_pack_test
```

## Security Considerations

### Repository Access Control
- Repository existence is validated before processing
- Path traversal attacks are prevented by proper path handling
- Only repositories within the configured root directory are accessible

### Resource Limits
- Request body size is limited by Axum configuration
- Git processes have reasonable timeouts
- Concurrent request limits prevent resource exhaustion

## Compatibility

### Git Client Support
- **Git 2.0+**: Full support for both protocols
- **libgit2**: Compatible with v1 protocol
- **JGit**: Compatible with v1 protocol
- **Custom clients**: Support depends on protocol compliance

### Repository Formats
- **Bare repositories**: Full support (recommended)
- **Non-bare repositories**: Limited support
- **Git LFS**: Requires additional configuration
- **Submodules**: Supported through git binary fallback

## Packet-Line Format

Git protocols use a packet-line format for structured communication:

### Format Structure
```
<4-byte length><payload><LF>
```

- **Length**: 4-byte hexadecimal string (including the 4 length bytes)
- **Payload**: Actual data content
- **LF**: Line feed character (0x0a)

### Special Packets
- **Flush packet**: `0000` - Indicates end of section
- **Delimiter packet**: `0001` - Separates sections in v2 protocol
- **Response end**: `0002` - Indicates end of response in v2 protocol

### Examples
```
003fHEAD refs/heads/main\0multi_ack thin-pack side-band
003e2cb58b79488a98d2721cea644875a8dd0026b115 refs/heads/main
0000
```

## Protocol Command Details

### ls-refs Command (Protocol v2)

**Purpose**: List repository references with optional filtering and metadata.

**Request Structure:**
```
0014command=ls-refs\n
0014agent=git/2.43.0\n
0001
000csymrefs\n
0009unborn\n
0000
```

**Response Structure:**
```
0050<sha1> HEAD symref-target:refs/heads/main\n
003d<sha1> refs/heads/main\n
003f<sha1> refs/heads/feature\n
0000
```

**Capabilities:**
- `symrefs`: Include symbolic reference targets
- `peel`: Include peeled tag information
- `unborn`: Include unborn HEAD for empty repositories

### fetch Command (Protocol v2)

**Purpose**: Transfer objects from repository to client.

**Implementation**: Delegated to git binary for complexity and reliability.

**Request Structure:**
```
0012command=fetch\n
0014agent=git/2.43.0\n
0001
0032want 2cb58b79488a98d2721cea644875a8dd0026b115\n
0000
```

## Implementation Examples

### Native ls-refs Implementation

```rust
async fn handle_ls_refs_command(repo_path: &Path, request_body: &[u8]) -> Result<Vec<u8>, String> {
    let repo = gix::open(repo_path)?;
    let mut buf = Vec::new();

    // Parse request options
    let request_str = String::from_utf8_lossy(request_body);
    let show_symrefs = request_str.contains("symrefs");

    // Handle HEAD explicitly
    if let Ok(head_ref) = repo.find_reference("HEAD") {
        if let Ok(peeled) = head_ref.clone().into_fully_peeled_id() {
            let mut line = format!("{} HEAD", peeled);

            if show_symrefs {
                if let Some(target_name) = head_ref.target().try_name() {
                    line.push_str(&format!(" symref-target:{}", target_name.as_bstr()));
                }
            }

            line.push('\n');
            write_packet_line(&mut buf, line.as_bytes())?;
        }
    }

    // Handle other references
    let refs = repo.refs.iter()?.all()?.collect::<Result<Vec<_>, _>>()?;
    for reference in refs {
        let name = reference.name.as_bstr();
        if name == "HEAD" { continue; } // Already handled

        let oid_str = match reference.target {
            gix::refs::Target::Object(oid) => oid.to_string(),
            gix::refs::Target::Symbolic(_) => {
                // Resolve symbolic references
                if let Ok(resolved) = repo.find_reference(name) {
                    if let Ok(peeled) = resolved.into_fully_peeled_id() {
                        peeled.to_string()
                    } else { continue; }
                } else { continue; }
            }
        };

        let line = format!("{} {}\n", oid_str, name);
        write_packet_line(&mut buf, line.as_bytes())?;
    }

    write_flush_packet(&mut buf)?;
    Ok(buf)
}
```

### Fallback Implementation

```rust
async fn handle_upload_pack_fallback(
    repo_path: &Path,
    body: &axum::body::Bytes,
    response_headers: HeaderMap,
) -> axum::response::Response {
    // Detect protocol version from request body
    let is_v2_request = String::from_utf8_lossy(body).contains("command=");

    let mut cmd = std::process::Command::new("git");
    cmd.arg("upload-pack")
        .arg("--stateless-rpc")
        .arg(".")
        .current_dir(repo_path);

    // Set protocol environment for v2 requests
    if is_v2_request {
        cmd.env("GIT_PROTOCOL", "version=2");
    }

    // Execute git command with request body as stdin
    let output = cmd
        .stdin(std::process::Stdio::piped())
        .stdout(std::process::Stdio::piped())
        .stderr(std::process::Stdio::piped())
        .spawn()?
        .wait_with_output()?;

    if output.status.success() {
        (StatusCode::OK, response_headers, output.stdout).into_response()
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        (StatusCode::INTERNAL_SERVER_ERROR, format!("Git failed: {}", stderr)).into_response()
    }
}
```

## Future Enhancements

### Planned Features
- **Partial clone support**: For large repositories
- **Bundle support**: For offline scenarios
- **Custom ref filters**: For security and performance
- **Metrics collection**: For monitoring and optimization

### Performance Optimizations
- **Reference caching**: For frequently accessed repositories
- **Parallel processing**: For multi-ref operations
- **Streaming responses**: For large reference lists
- **Connection pooling**: For git binary fallback
