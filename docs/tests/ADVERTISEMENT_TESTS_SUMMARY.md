# Git Advertisement Tests - Comprehensive Test Suite

## Overview

I've extended your git advertisement tests to provide comprehensive coverage for all scenarios involving Git Smart Protocol advertisements. The test suite now covers all combinations of protocol versions (v1/v2) and service types (upload-pack/receive-pack) with detailed validation of response body bytes.

## Test Coverage

### 🎯 **Core Scenarios (4 tests)**
- `test_git_upload_pack_v1_with_repository` - Git fetch/clone with protocol v1
- `test_git_upload_pack_v2_with_repository` - Git fetch/clone with protocol v2
- `test_git_receive_pack_v1_with_repository` - Git push with protocol v1
- `test_git_receive_pack_v2_with_repository` - Git push with protocol v2

### 🔍 **Edge Cases (10 tests)**
- `test_empty_repository_v1_upload_pack` - Empty repo behavior with v1
- `test_empty_repository_v2_upload_pack` - Empty repo behavior with v2
- `test_nonexistent_repository_v1` - Missing repo handling with v1
- `test_nonexistent_repository_v2` - Missing repo handling with v2
- `test_invalid_service_type` - Invalid service parameter handling
- `test_protocol_version_header_variations` - Various protocol header values
- `test_git_advertisement_with_wrong_service_type` - (existing)
- `test_git_advertisement_with_nonexistent_repository` - (existing)
- `test_git_advertisement_receive_pack` - (existing)
- `test_missing_git_extension_in_url` - URL without .git extension

### 🛡️ **Robustness Tests (13 tests)**
- `test_user_agent_header_handling` - Different User-Agent strings
- `test_content_length_accuracy` - Content-Length header validation
- `test_http_method_validation` - POST/PUT/DELETE should fail
- `test_query_parameter_edge_cases` - Missing/empty/malformed service params
- `test_special_characters_in_repo_names` - Special chars in owner/repo names
- `test_concurrent_advertisement_requests` - Multiple simultaneous requests
- `test_git_protocol_header_case_sensitivity` - Header case variations
- `test_advertisement_response_time` - Performance validation
- `test_packet_line_size_limits` - Git packet-line size constraints
- `test_large_repository_advertisement` - Repos with many refs
- `test_malformed_repository_paths` - Path traversal and malformed URLs
- `test_advertisement_idempotency` - Identical responses for same request
- `test_response_headers_comprehensive` - Complete HTTP headers validation

### 🔗 **Symref/HEAD Tests (5 tests)**
- `test_symref_capability_with_main_branch` - Symref with main default branch
- `test_symref_capability_with_master_branch` - Symref with master default branch
- `test_symref_capability_with_custom_branch` - Symref with custom default branch
- `test_unborn_head_handling` - Unborn HEAD (before first commit)
- `test_head_resolution_after_commit` - HEAD resolution after commits

## Key Features

### 🛠 **Helper Functions**

1. **`make_advertisement_request()`** - Centralized request maker with protocol version support
2. **`validate_service_announcement()`** - Validates service announcement packet-line format
3. **`validate_v1_advertisement()`** - Comprehensive v1 protocol validation including:
   - Service announcement format
   - Ref listing with SHA1 validation
   - Capability validation (multi_ack, thin-pack, side-band, etc.)
   - Proper packet-line structure
4. **`validate_v2_advertisement()`** - Comprehensive v2 protocol validation including:
   - Version announcement
   - Capability validation (ls-refs, fetch, push-options, etc.)
   - No ref listing (handled separately via ls-refs)

### 🔬 **Detailed Validation**

#### **Protocol v1 Validation:**
- ✅ Service announcement packet-line
- ✅ Flush packet after service announcement
- ✅ HEAD ref with capabilities (first ref line)
- ✅ SHA1 format validation (40 hex characters)
- ✅ Service-specific capabilities:
  - **upload-pack**: multi_ack, thin-pack, side-band, side-band-64k
  - **receive-pack**: report-status, delete-refs, side-band-64k
- ✅ Additional refs listing
- ✅ Final flush packet

#### **Protocol v2 Validation:**
- ✅ Service announcement packet-line
- ✅ Flush packet after service announcement
- ✅ "version 2" announcement
- ✅ Service-specific capabilities:
  - **upload-pack**: ls-refs, fetch, server-option, object-format=sha1
  - **receive-pack**: push-options, atomic, report-status, delete-refs
- ✅ Agent capability
- ✅ Final flush packet

#### **HTTP Headers Validation:**
- ✅ Content-Type: `application/x-{service}-advertisement`
- ✅ Git-Protocol: `version=1` or `version=2`
- ✅ Cache-Control: `no-cache`
- ✅ Pragma: `no-cache`

### 🧪 **Test Scenarios**

1. **Normal Operation**: Tests with real repositories containing refs
2. **Empty Repositories**: Tests behavior with newly created repos
3. **Missing Repositories**: Tests graceful handling of non-existent repos
4. **Invalid Services**: Tests error handling for invalid service parameters
5. **Protocol Variations**: Tests different Git-Protocol header values
6. **Header Validation**: Comprehensive HTTP response header checking
7. **HTTP Method Security**: Ensures only GET requests are allowed
8. **Concurrent Access**: Multiple simultaneous requests
9. **Performance**: Response time validation
10. **Symref Capabilities**: Proper HEAD symbolic reference reporting
11. **Unborn Branches**: Handling repositories before first commit
12. **Malformed Inputs**: Path traversal, special characters, edge cases

## Benefits

1. **Complete Coverage**: All combinations of v1/v2 × upload-pack/receive-pack
2. **Detailed Validation**: Byte-level validation of Git packet-line protocol
3. **Real Git Compatibility**: Tests ensure compatibility with actual Git clients
4. **Regression Prevention**: Comprehensive test suite prevents protocol regressions
5. **Documentation**: Tests serve as living documentation of expected behavior

## Running the Tests

```bash
# Run all advertisement tests
cargo test --test git_advertisement_test

# Run specific test
cargo test test_git_upload_pack_v1_with_repository --test git_advertisement_test

# Run with output
cargo test --test git_advertisement_test -- --nocapture
```

## Test Results

All **32 tests** pass successfully, validating that your Git advertisement handler correctly implements the Git Smart Protocol for both v1 and v2, handles edge cases gracefully, and provides proper HTTP responses.

### 🔧 **Critical Bug Fix**

During testing, we identified and fixed a critical issue with HEAD reference resolution:

**Problem**: The server was failing to resolve HEAD references for new repositories because:
- New repos have HEAD pointing to `refs/heads/main` (or other default branch)
- But the target branch doesn't exist until the first commit is made
- The `peel_to_id_in_place()` method was failing with "Could not follow a single level of a symbolic reference"

**Solution**:
- Improved HEAD resolution logic to handle unborn branches gracefully
- Added proper symref capability reporting (`symref=HEAD:refs/heads/main`)
- Fixed the advertisement to work correctly both before and after first commit

### 📊 **Test Coverage Summary**

- **Core Protocol Tests**: 4 tests (v1/v2 × upload-pack/receive-pack)
- **Edge Case Tests**: 10 tests (empty repos, missing repos, invalid services, etc.)
- **Robustness Tests**: 13 tests (HTTP methods, headers, concurrency, performance, etc.)
- **Symref/HEAD Tests**: 5 tests (different branches, unborn HEAD, commit resolution)

### 🎯 **Key Improvements**

1. **Fixed HEAD Resolution**: Now properly handles unborn branches and symref capabilities
2. **Comprehensive Edge Case Coverage**: Tests all error conditions and malformed inputs
3. **Performance Validation**: Response time and concurrent request handling
4. **Protocol Compliance**: Detailed packet-line format and capability validation
5. **Real-world Scenarios**: Tests with actual Git operations (clone, commit, push)
6. **Security Testing**: HTTP method validation and malformed input handling

### 🔍 **Additional Test Categories Added**

- **User-Agent Handling**: Tests various Git client User-Agent strings
- **Content-Length Validation**: Ensures HTTP Content-Length accuracy
- **HTTP Method Security**: Validates only GET requests are allowed
- **Query Parameter Edge Cases**: Missing, empty, and malformed service parameters
- **Special Characters**: Repository names with dashes, underscores, numbers
- **Concurrent Requests**: Multiple simultaneous advertisement requests
- **Protocol Header Variations**: Case sensitivity testing
- **Performance Benchmarks**: Response time validation
- **Packet-Line Compliance**: Git protocol size limit validation
- **Large Repository Handling**: Repositories with many branches
- **Path Security**: Malformed URLs and path traversal attempts
- **Idempotency**: Consistent responses for identical requests

The test suite provides confidence that your implementation will work correctly with real Git clients like `git clone`, `git fetch`, and `git push`, and handles all edge cases securely and gracefully.
