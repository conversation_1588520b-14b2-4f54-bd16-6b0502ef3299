# Git Filtering Support

This document describes the filtering capabilities supported by the Git server for partial clone and shallow clone operations.

## Overview

Git filtering allows clients to request only specific parts of a repository, which is useful for:
- Large repositories with many binary files
- CI/CD systems that only need recent commits
- Sparse checkouts for monorepos
- Bandwidth-limited environments

## Supported Filter Types

### 1. Blob Filters

#### `blob:none`
Excludes all blobs (file contents), only downloads trees and commits.

**Use case**: When you only need the repository structure and commit history.

```bash
# Clone without any file contents
git clone --filter=blob:none https://your-server.com/owner/repo.git

# Fetch specific files on demand
git checkout HEAD -- path/to/file.txt
```

#### `blob:limit=<size>`
Only includes blobs smaller than the specified size.

**Use case**: Exclude large binary files while keeping small text files.

```bash
# Clone excluding files larger than 1MB
git clone --filter=blob:limit=1048576 https://your-server.com/owner/repo.git

# Clone excluding files larger than 100KB
git clone --filter=blob:limit=102400 https://your-server.com/owner/repo.git
```

### 2. Tree Filters

#### `tree:0`
Excludes all trees and blobs, only downloads commits.

**Use case**: When you only need commit history and metadata.

```bash
# Clone only commit history
git clone --filter=tree:0 https://your-server.com/owner/repo.git
```

### 3. Combined Filters

#### `combine:<filter1>+<filter2>`
Combines multiple filters using AND logic.

**Use case**: Apply multiple filtering criteria simultaneously.

```bash
# Exclude all blobs AND limit tree depth
git clone --filter=combine:blob:none+tree:1 https://your-server.com/owner/repo.git
```

### 4. Sparse Filters (Experimental)

#### `sparse:<path1>,<path2>`
Only includes specified paths.

**Use case**: Monorepo scenarios where you only need specific directories.

```bash
# Only include specific directories
git clone --filter=sparse:src/,docs/ https://your-server.com/owner/repo.git
```

## Shallow Clone Support

### Depth-based Shallow Clones

```bash
# Clone only the last 10 commits
git clone --depth=10 https://your-server.com/owner/repo.git

# Clone only the last commit
git clone --depth=1 https://your-server.com/owner/repo.git
```

### Time-based Shallow Clones

```bash
# Clone commits since a specific date
git clone --shallow-since="2023-01-01" https://your-server.com/owner/repo.git

# Clone commits until a specific date
git clone --shallow-until="2023-12-31" https://your-server.com/owner/repo.git
```

### Deepen Operations

```bash
# Deepen an existing shallow clone by 5 more commits
git fetch --deepen=5

# Deepen to include all commits since a date
git fetch --deepen-since="2023-01-01"
```

## Protocol Support

### Git Protocol v2

The server supports filtering through Git protocol v2 fetch commands:

```
command=fetch
want <commit-sha>
filter blob:none
done
```

### Git Protocol v1

For v1 clients, filtering is handled through git binary fallback with appropriate command-line arguments.

## Implementation Details

### Server-side Processing

1. **Request Parsing**: The server parses filter specifications from the fetch request
2. **Filter Validation**: Validates that the requested filters are supported
3. **Git Binary Integration**: Uses git's native filtering capabilities for reliable results
4. **Response Generation**: Returns filtered pack files according to the client's specifications

### Supported Filter Combinations

| Filter Type | Blob None | Blob Limit | Tree Depth | Combine | Sparse |
|-------------|-----------|------------|------------|---------|--------|
| **Blob None** | ✅ | ❌ | ✅ | ✅ | ✅ |
| **Blob Limit** | ❌ | ✅ | ✅ | ✅ | ✅ |
| **Tree Depth** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Combine** | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Sparse** | ✅ | ✅ | ✅ | ✅ | ⚠️ |

⚠️ = Experimental support

## Client Examples

### Using Git CLI

```bash
# Basic partial clone
git clone --filter=blob:none https://your-server.com/owner/repo.git
cd repo

# Fetch specific files when needed
git checkout HEAD -- README.md
git checkout HEAD -- src/main.rs

# Check what's missing
git rev-list --objects --missing=print HEAD
```

### Using libgit2 (Rust)

```rust
use git2::{Repository, FetchOptions, RemoteCallbacks};

let mut fetch_options = FetchOptions::new();
let mut remote_callbacks = RemoteCallbacks::new();

// Set up authentication if needed
remote_callbacks.credentials(|_url, username_from_url, _allowed_types| {
    // Your authentication logic here
    Ok(())
});

fetch_options.remote_callbacks(remote_callbacks);

// Clone with blob filter
let repo = Repository::clone_recurse(
    "https://your-server.com/owner/repo.git",
    Path::new("./repo"),
)?;
```

## Performance Considerations

### Bandwidth Savings

| Repository Size | Filter | Bandwidth Reduction |
|----------------|--------|-------------------|
| 1GB with large binaries | `blob:none` | ~80-95% |
| 500MB mixed content | `blob:limit=1MB` | ~40-60% |
| Deep history (1000+ commits) | `--depth=10` | ~70-90% |

### Server Resources

- **CPU**: Filtering requires additional processing but is generally lightweight
- **Memory**: Filtered operations use less memory for pack generation
- **Disk I/O**: Reduced for filtered operations
- **Network**: Significantly reduced bandwidth usage

## Troubleshooting

### Common Issues

1. **"Filter not supported"**: Ensure your Git client supports the filter type
2. **"Missing objects"**: Use `git fetch --unshallow` or fetch specific objects
3. **"Protocol error"**: Check that both client and server support protocol v2

### Debugging

Enable debug logging to see filter processing:

```bash
RUST_LOG=debug cargo run
```

Look for log messages like:
```
Fetch request: wants=1, haves=0, filter=Some(BlobNone), shallow_depth=None
Processing filtered fetch request with filter: blob:none
Filtered fetch completed successfully with filter: blob:none
```

## Future Enhancements

- **Custom filter plugins**: Support for repository-specific filtering logic
- **Filter caching**: Cache filtered pack files for improved performance
- **Advanced sparse filters**: Better support for complex path patterns
- **Filter statistics**: Metrics on filter usage and effectiveness
