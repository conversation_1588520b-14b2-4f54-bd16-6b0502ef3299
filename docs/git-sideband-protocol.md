# Git Sideband Protocol Implementation

## Overview

This document explains the Git sideband protocol implementation for receive-pack operations, discovered through byte-level analysis of the official Git binary.

## Problem Statement

When implementing a native Git receive-pack server, we encountered the "bad band #117" error when clients negotiated the `side-band-64k` capability. This led to a deep investigation of the exact protocol format expected by Git clients.

## Protocol Discovery Process

### 1. Initial Approach (Failed)
We initially tried sending status reports through sideband channel 2 (progress messages):
```
PKT-LINE(CHANNEL_2 + "unpack ok\n")
PKT-LINE(CHANNEL_2 + "ok refs/heads/branch\n") 
PKT-LINE(flush)
```

**Result**: "bad band #117" error (117 = ASCII 'u' from "unpack")

### 2. Byte-Level Analysis
We captured the exact responses from both our implementation and the official Git binary:

#### Git Binary Response (Working):
```
Hex: 303033380130303065756e7061636b206f6b0a303032316f6b20726566732f68656164732f666561747572652d6272616e63680a3030303030303030
Length: 60 bytes
```

#### Our Implementation Response (Failing):
```
Hex: 3030306602756e7061636b206f6b0a30303232026f6b20726566732f68656164732f666561747572652d6272616e63680a30303030
Length: 53 bytes
```

### 3. Protocol Decoding

Breaking down the Git binary response:

```
0038    - Outer packet length (56 bytes)
01      - Sideband channel 1 (pack data)
000e    - Inner packet length (14 bytes)
756e7061636b206f6b0a - "unpack ok\n"
0021    - Inner packet length (33 bytes) 
6f6b20726566732f68656164732f666561747572652d6272616e63680a - "ok refs/heads/feature-branch\n"
0000    - Inner flush packet
0000    - Outer flush packet
0000    - Final flush packet
```

## Key Discoveries

### 1. Sideband Channel Usage
- **Wrong**: Channel 2 (progress messages)
- **Correct**: Channel 1 (pack data)

### 2. Nested Packet Structure
Status reports are not sent directly through sideband. Instead:
1. Build regular packet lines for status report
2. Wrap the entire packet stream in a sideband channel 1 packet
3. Terminate with double flush packets

### 3. Correct Implementation

```rust
// Step 1: Build nested packet lines
let mut nested_packets = Vec::new();
write_packet_line(&mut nested_packets, "unpack ok\n".as_bytes())?;
write_packet_line(&mut nested_packets, "ok refs/heads/branch\n".as_bytes())?;
write_flush_packet(&mut nested_packets)?;

// Step 2: Wrap in sideband channel 1
let mut sideband_data = vec![1u8]; // Channel 1
sideband_data.extend_from_slice(&nested_packets);
write_packet_line(&mut buf, &sideband_data)?;

// Step 3: Double flush termination
write_flush_packet(&mut buf)?;
write_flush_packet(&mut buf)?;
```

## Protocol Comparison

| Aspect | Without Sideband | With Sideband |
|--------|------------------|---------------|
| Status Format | Direct packet lines | Nested in sideband channel 1 |
| Termination | Single flush | Double flush |
| Complexity | Simple | Complex nested structure |

## Testing Results

After implementing the correct protocol:
- ✅ All Git operations work correctly
- ✅ No more "bad band" errors  
- ✅ No more "hung up unexpectedly" errors
- ✅ 10/11 tests passing (1 fails due to unimplemented feature)

## References

- [Git Pack Protocol Documentation](https://git-scm.com/docs/pack-protocol)
- [Git Protocol Capabilities](https://git-scm.com/docs/protocol-capabilities)
- Byte-level analysis using `GIT_TRACE_PACKET=1`
