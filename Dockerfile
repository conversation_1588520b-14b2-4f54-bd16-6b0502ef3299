FROM rust:1.87-slim AS gitoxide-builder

RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

RUN useradd arm -u 1000 -m -s /bin/bash
USER arm

RUN cargo install gitoxide

FROM rust:1.87-slim AS dev

ARG SERVICE_PORT
ENV SERVICE_PORT=${SERVICE_PORT}

ARG SERVICE_HOST
ENV SERVICE_HOST=${SERVICE_HOST}

ARG MAX_BODY_SIZE
ENV MAX_BODY_SIZE=${MAX_BODY_SIZE:-524288000}

ENV REPOS_ROOT="/app/repos"

WORKDIR /app/

RUN apt-get update && apt-get install -y \
    cmake \
    pkg-config \
    libssl-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

RUN rustup default

RUN useradd arm -u 1000 -m -s /bin/bash

COPY --from=gitoxide-builder /usr/local/cargo/bin/gix /usr/local/bin/gix

COPY . .
RUN chown -R arm:arm /app/

USER arm

RUN mkdir -p /app/repos

ENV PATH="/home/<USER>/.cargo/bin:${PATH}"

EXPOSE 5500

CMD ["cargo", "run"]

FROM rust:1.87-slim AS builder

WORKDIR /app/

RUN apt-get update && apt-get install -y cmake \
    && rm -rf /var/lib/apt/lists/*

COPY . .
RUN rustup default

RUN useradd arm -u 1000

EXPOSE 5500

RUN cargo build --release

CMD ["echo", "whatever"]

FROM ubuntu:24.04 AS production

ARG SERVICE_PORT
ENV SERVICE_PORT=${SERVICE_PORT}

ARG SERVICE_HOST
ENV SERVICE_HOST=${SERVICE_HOST}

ARG MAX_BODY_SIZE
ENV MAX_BODY_SIZE=${MAX_BODY_SIZE:-524288000}

WORKDIR /srv/app


COPY --from=builder /app/target/release/axum-git-server /srv/app/

EXPOSE 5500

CMD ["./axum-git-server"]

# docker build -t axum-git-server .
# docker run -it -p 5500:5500 axum-git-server
