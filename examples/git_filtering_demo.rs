use axum_git_server::types::git::filter::{GitFilter, parse_fetch_request, FetchRequest};

/// Demonstrates Git filtering capabilities
fn main() {
    println!("🔍 Git Filtering Demo");
    println!("====================\n");

    // Demo 1: Basic filter parsing
    demo_filter_parsing();
    
    // Demo 2: Fetch request parsing
    demo_fetch_request_parsing();
    
    // Demo 3: Filter properties
    demo_filter_properties();
    
    // Demo 4: Combined filters
    demo_combined_filters();
    
    // Demo 5: Practical use cases
    demo_practical_use_cases();
}

fn demo_filter_parsing() {
    println!("📋 1. Filter Parsing Examples");
    println!("-----------------------------");
    
    let filters = vec![
        "blob:none",
        "blob:limit=1048576",
        "tree:0",
        "tree:1",
        "combine:blob:none+tree:1",
        "sparse:src/,docs/,README.md",
    ];
    
    for filter_str in filters {
        match GitFilter::parse(filter_str) {
            Ok(filter) => {
                println!("✅ '{}' -> {:?}", filter_str, filter);
                println!("   Display: {}", filter);
            }
            Err(e) => {
                println!("❌ '{}' -> Error: {}", filter_str, e);
            }
        }
    }
    println!();
}

fn demo_fetch_request_parsing() {
    println!("📦 2. Fetch Request Parsing");
    println!("---------------------------");
    
    let request_examples = vec![
        // Basic fetch with blob filter
        "command=fetch\nwant abc123def456\nfilter blob:none\ndone\n",
        
        // Fetch with size limit
        "command=fetch\nwant abc123\nhave def456\nfilter blob:limit=1048576\ndone\n",
        
        // Shallow fetch
        "command=fetch\nwant abc123\nshallow 10\ndone\n",
        
        // Combined filter fetch
        "command=fetch\nwant abc123\nfilter combine:blob:none+tree:1\ndone\n",
        
        // Multiple wants and haves
        "command=fetch\nwant abc123\nwant def456\nhave 111222\nhave 333444\nfilter blob:none\ndone\n",
    ];
    
    for (i, request_body) in request_examples.iter().enumerate() {
        println!("Example {}:", i + 1);
        match parse_fetch_request(request_body.as_bytes()) {
            Ok(request) => {
                println!("  ✅ Parsed successfully:");
                println!("     Wants: {:?}", request.wants);
                println!("     Haves: {:?}", request.haves);
                println!("     Filter: {:?}", request.filter);
                println!("     Shallow depth: {:?}", request.shallow.depth);
                println!("     Done: {}", request.done);
            }
            Err(e) => {
                println!("  ❌ Parse error: {}", e);
            }
        }
        println!();
    }
}

fn demo_filter_properties() {
    println!("🔧 3. Filter Properties");
    println!("----------------------");
    
    let filters = vec![
        GitFilter::BlobNone,
        GitFilter::BlobLimit(1024),
        GitFilter::TreeDepth(0),
        GitFilter::TreeDepth(1),
        GitFilter::Combine(vec![
            GitFilter::BlobNone,
            GitFilter::TreeDepth(1)
        ]),
    ];
    
    for filter in filters {
        println!("Filter: {}", filter);
        println!("  Excludes blobs: {}", filter.excludes_blobs());
        println!("  Excludes trees: {}", filter.excludes_trees());
        println!("  Max blob size: {:?}", filter.max_blob_size());
        println!();
    }
}

fn demo_combined_filters() {
    println!("🔗 4. Combined Filters");
    println!("---------------------");
    
    // Example: Exclude all blobs but limit tree depth
    let filter1 = GitFilter::Combine(vec![
        GitFilter::BlobNone,
        GitFilter::TreeDepth(1)
    ]);
    println!("Filter 1: {}", filter1);
    println!("  Use case: Get only commit history and top-level directory structure");
    println!("  Excludes blobs: {}", filter1.excludes_blobs());
    println!("  Excludes trees: {}", filter1.excludes_trees());
    println!();
    
    // Example: Limit blob size and tree depth
    let filter2 = GitFilter::Combine(vec![
        GitFilter::BlobLimit(102400), // 100KB
        GitFilter::TreeDepth(2)
    ]);
    println!("Filter 2: {}", filter2);
    println!("  Use case: Get small files and limited directory depth");
    println!("  Max blob size: {:?}", filter2.max_blob_size());
    println!();
}

fn demo_practical_use_cases() {
    println!("💡 5. Practical Use Cases");
    println!("------------------------");
    
    println!("🎯 Use Case 1: CI/CD Pipeline (only need source code)");
    let ci_filter = GitFilter::BlobLimit(1048576); // 1MB limit
    println!("   Filter: {}", ci_filter);
    println!("   Command: git clone --filter={} <repo>", ci_filter);
    println!("   Benefit: Excludes large binaries, keeps source files");
    println!();
    
    println!("🎯 Use Case 2: Code Analysis (structure only)");
    let analysis_filter = GitFilter::BlobNone;
    println!("   Filter: {}", analysis_filter);
    println!("   Command: git clone --filter={} <repo>", analysis_filter);
    println!("   Benefit: Fast clone for analyzing repository structure");
    println!();
    
    println!("🎯 Use Case 3: Shallow Development (recent history)");
    println!("   Command: git clone --depth=10 <repo>");
    println!("   Benefit: Only get last 10 commits for quick development");
    println!();
    
    println!("🎯 Use Case 4: Monorepo Subset (specific directories)");
    let monorepo_filter = GitFilter::Sparse(vec![
        "services/api/".to_string(),
        "shared/utils/".to_string(),
        "README.md".to_string()
    ]);
    println!("   Filter: {}", monorepo_filter);
    println!("   Benefit: Only get relevant parts of a large monorepo");
    println!();
    
    println!("🎯 Use Case 5: Bandwidth-Limited Environment");
    let bandwidth_filter = GitFilter::Combine(vec![
        GitFilter::BlobLimit(51200), // 50KB
        GitFilter::TreeDepth(1)
    ]);
    println!("   Filter: {}", bandwidth_filter);
    println!("   Benefit: Minimal download for slow connections");
    println!();
}

/// Example of how to use filters in a real application
#[allow(dead_code)]
fn example_application_usage() {
    // Parse a client request
    let request_body = b"command=fetch\nwant abc123\nfilter blob:limit=1048576\ndone\n";
    
    match parse_fetch_request(request_body) {
        Ok(request) => {
            if let Some(filter) = &request.filter {
                match filter {
                    GitFilter::BlobNone => {
                        println!("Client wants structure only - very fast response");
                    }
                    GitFilter::BlobLimit(size) => {
                        println!("Client wants files up to {} bytes", size);
                    }
                    GitFilter::TreeDepth(depth) => {
                        println!("Client wants {} levels of directory structure", depth);
                    }
                    GitFilter::Combine(filters) => {
                        println!("Client wants combined filtering: {:?}", filters);
                    }
                    GitFilter::Sparse(paths) => {
                        println!("Client wants specific paths: {:?}", paths);
                    }
                }
                
                // Server can now optimize the response based on the filter
                // - Skip large files for blob:limit
                // - Skip file contents for blob:none
                // - Limit directory traversal for tree:depth
            }
        }
        Err(e) => {
            println!("Invalid request: {}", e);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_demo_examples() {
        // Test that all the demo examples work correctly
        
        // Test filter parsing
        assert!(GitFilter::parse("blob:none").is_ok());
        assert!(GitFilter::parse("blob:limit=1024").is_ok());
        assert!(GitFilter::parse("tree:0").is_ok());
        
        // Test fetch request parsing
        let request_body = b"command=fetch\nwant abc123\nfilter blob:none\ndone\n";
        let request = parse_fetch_request(request_body).unwrap();
        assert_eq!(request.filter, Some(GitFilter::BlobNone));
        
        // Test combined filters
        let combined = GitFilter::Combine(vec![
            GitFilter::BlobNone,
            GitFilter::TreeDepth(1)
        ]);
        assert!(combined.excludes_blobs());
        assert!(!combined.excludes_trees());
    }
}
