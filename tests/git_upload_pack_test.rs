use axum_git_server::error::AppError;
use axum_git_server::helpers::git_pkt::{
    write_delimiter_packet, write_flush_packet, write_packet_line,
};
use tokio::process::Command;

#[cfg(test)]
mod common;
use common::test_helpers::{
    clone_repo_to_temp, create_branch_with_empty_commit_and_push, create_random_repo_identifier,
    create_test_repo_through_api,
};

/// Test basic upload-pack request for protocol v1
#[tokio::test]
async fn test_upload_pack_request_v1() {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Test upload-pack request (v1 uses empty request body)
    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .body("0000")
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    assert_eq!(
        response.headers().get("content-type").unwrap(),
        "application/x-git-upload-pack-result"
    );
}

/// Test basic upload-pack request for protocol v2
#[tokio::test]
async fn test_upload_pack_request_v2() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Test upload-pack request with v2 ls-refs command
    // let ls_refs_request = "0014command=ls-refs\n0014agent=git/2.43.0\n0000";
    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    // write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    // write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    // write_packet_line(&mut buff, "unborn\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    assert_eq!(
        response.headers().get("content-type").unwrap(),
        "application/x-git-upload-pack-result"
    );
    Ok(())
}

/// Test upload-pack with nonexistent repository
#[tokio::test]
async fn test_upload_pack_nonexistent_repository() {
    let client = reqwest::Client::new();
    let response = client
        .post("http://0.0.0.0:5500/nonexistent/repo.git/git-upload-pack")
        .body("0000")
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 404);
}

/// Test upload-pack ls-refs command (protocol v2) with repository that has commits
#[tokio::test]
async fn test_upload_pack_ls_refs_command_with_commits() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Create a test commit in the repository using clone approach
    let config = axum_git_server::config::load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier)
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "main", "Initial commit")
        .await
        .unwrap();

    // Test ls-refs command
    // let ls_refs_request =
    // "0014command=ls-refs\n0014agent=git/2.43.0\n0001000csymrefs\n0009unborn\n0000";

    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_packet_line(&mut buff, "unborn\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    let body = response.text().await.unwrap();

    // Should contain refs in the response
    assert!(body.len() > 0);
    // Response should be properly packet-line formatted
    assert!(body.starts_with("00"));
    Ok(())
}

/// Test upload-pack ls-refs command (protocol v2) with empty repository
#[tokio::test]
async fn test_upload_pack_ls_refs_command_empty_repo() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API (but don't add any commits)
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Test ls-refs command on empty repository
    // let ls_refs_request =
    // "0014command=ls-refs\n0014agent=git/2.43.0\n0001000csymrefs\n0009unborn\n0000";

    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_packet_line(&mut buff, "unborn\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    let body = response.text().await.unwrap();

    // Empty repo should return unborn HEAD
    assert!(body.contains("unborn HEAD"));
    Ok(())
}

/// Test upload-pack with empty repository
#[tokio::test]
async fn test_upload_pack_empty_repository() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Test ls-refs on empty repository
    // let ls_refs_request =
    // "0014command=ls-refs\n0014agent=git/2.43.0\n0001000csymrefs\n0009unborn\n0000";

    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_packet_line(&mut buff, "unborn\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    let body = response.text().await.unwrap();

    // Empty repo should return unborn HEAD
    assert!(body.contains("unborn HEAD"));
    Ok(())
}

/// Test upload-pack with multiple branches
#[tokio::test]
async fn test_upload_pack_multiple_branches() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Create commits using clone approach
    let config = axum_git_server::config::load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier)
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "main", "Initial commit")
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "feature/test", "Feature commit")
        .await
        .unwrap();

    // Test ls-refs command
    // let ls_refs_request = "0014command=ls-refs\n0014agent=git/2.43.0\n0001000csymrefs\n0000";

    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    // write_packet_line(&mut buff, "peel\n".as_bytes())?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    let body = response.text().await.unwrap();

    // Should contain both branches
    assert!(body.contains("refs/heads/main"));
    assert!(body.contains("refs/heads/feature/test"));
    assert!(body.contains("HEAD"));
    Ok(())
}

/// Test upload-pack protocol version handling
#[tokio::test]
async fn test_upload_pack_protocol_versions() {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    let client = reqwest::Client::new();

    // Test without Git-Protocol header (should default to v1)
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .body("0000")
        .send()
        .await
        .unwrap();

    println!("RES HEADERS: {:?}", response.headers());
    assert_eq!(response.status(), 200);

    // Test with explicit v1
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=1")
        .body("0000")
        .send()
        .await
        .unwrap();

    println!("RES HEADERS: {:?}", response.headers());
    assert_eq!(response.status(), 200);

    // Test with v2
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .body("0014command=ls-refs\n0000")
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
}

/// Test upload-pack error handling
#[tokio::test]
async fn test_upload_pack_error_handling() {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    let client = reqwest::Client::new();

    // Test with invalid v2 command
    let invalid_request = "0018command=invalid-cmd\n0000";

    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .body(invalid_request)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 500);
}

/// Test upload-pack with tags
#[tokio::test]
async fn test_upload_pack_with_tags() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Create commits and tags using clone approach
    let config = axum_git_server::config::load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier)
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "main", "Initial commit")
        .await
        .unwrap();

    // Create a tag
    let output = Command::new("git")
        .args(&["tag", "v1.0.0", "-m", "Version 1.0.0"])
        .current_dir(&clone_path)
        .output()
        .await
        .unwrap();
    assert!(output.status.success());

    // Push the tag
    let output = Command::new("git")
        .args(&["push", "origin", "v1.0.0"])
        .current_dir(&clone_path)
        .output()
        .await
        .unwrap();
    assert!(output.status.success());

    // Test ls-refs command with peel option
    let mut buff = Vec::new();
    // Not needed
    // write_packet_line(&mut buff, "version 2\n".as_bytes())?;
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    write_packet_line(&mut buff, "peel\n".as_bytes())?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    let body = response.text().await.unwrap();

    // Should contain the tag
    assert!(body.contains("refs/tags/v1.0.0"));
    Ok(())
}

/// Test upload-pack symref capability
#[tokio::test]
async fn test_upload_pack_symref_capability() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Create commits using clone approach
    let config = axum_git_server::config::load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier)
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "main", "Initial commit")
        .await
        .unwrap();

    // Test ls-refs command with symrefs
    // let ls_refs_request = "0014command=ls-refs\n0014agent=git/2.43.0\n0001000csymrefs\n0000";

    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .header("Content-Type", "application/x-git-upload-pack-request")
        .body(buff)
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    let body = response.text().await.unwrap();

    // Should contain symref information for HEAD
    assert!(body.contains("HEAD"));
    assert!(body.contains("symref-target:refs/heads/main"));
    Ok(())
}

/// Test upload-pack concurrent requests
#[tokio::test]
async fn test_upload_pack_concurrent_requests() -> Result<(), AppError> {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    // Create commits using clone approach
    let config = axum_git_server::config::load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier)
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "main", "Initial commit")
        .await
        .unwrap();

    // Test concurrent ls-refs requests
    let mut buff = Vec::new();
    // Not needed
    write_packet_line(&mut buff, "command=ls-refs\n".as_bytes())?;
    write_packet_line(&mut buff, "agent=git/2.43.0".as_bytes())?;
    write_packet_line(&mut buff, "object-format=sha1".as_bytes())?;
    write_delimiter_packet(&mut buff)?;
    write_packet_line(&mut buff, "symrefs\n".as_bytes())?;
    write_flush_packet(&mut buff)?;

    let client = reqwest::Client::new();
    let mut handles = vec![];

    for _ in 0..5 {
        let client = client.clone();
        let repo_identifier = repo_identifier.clone();
        // let ls_refs_request = ls_refs_request.to_string();
        let buff = buff.clone();

        let handle = tokio::spawn(async move {
            client
                .post(&format!(
                    "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
                    repo_identifier.owner, repo_identifier.repo
                ))
                .header("Git-Protocol", "version=2")
                .header("Content-Type", "application/x-git-upload-pack-request")
                .body(buff)
                .send()
                .await
                .unwrap()
        });

        handles.push(handle);
    }

    // Wait for all requests to complete
    for handle in handles {
        let response = handle.await.unwrap();
        assert_eq!(response.status(), 200);
    }
    Ok(())
}

/// Test upload-pack response headers
#[tokio::test]
async fn test_upload_pack_response_headers() {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    let client = reqwest::Client::new();

    // Test v1 response headers
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .body("0000")
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    assert_eq!(
        response.headers().get("content-type").unwrap(),
        "application/x-git-upload-pack-result"
    );
    assert_eq!(response.headers().get("cache-control").unwrap(), "no-cache");
    assert_eq!(response.headers().get("pragma").unwrap(), "no-cache");

    // Test v2 response headers
    let response = client
        .post(&format!(
            "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
            repo_identifier.owner, repo_identifier.repo
        ))
        .header("Git-Protocol", "version=2")
        .body("0014command=ls-refs\n0000")
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), 200);
    assert_eq!(
        response.headers().get("content-type").unwrap(),
        "application/x-git-upload-pack-result"
    );
    assert_eq!(response.headers().get("cache-control").unwrap(), "no-cache");
    assert_eq!(response.headers().get("pragma").unwrap(), "no-cache");
}

/// Test upload-pack user agent handling
#[tokio::test]
async fn test_upload_pack_user_agent() {
    let repo_identifier = create_random_repo_identifier();

    // Create repository through API
    create_test_repo_through_api(&repo_identifier, "main").await;

    let client = reqwest::Client::new();

    // Test with different user agents
    let user_agents = vec![
        "git/2.43.0",
        "git/2.40.1",
        "libgit2 1.7.2",
        "custom-git-client/1.0",
    ];

    for user_agent in user_agents {
        let response = client
            .post(&format!(
                "http://0.0.0.0:5500/{}/{}.git/git-upload-pack",
                repo_identifier.owner, repo_identifier.repo
            ))
            .header("User-Agent", user_agent)
            .body("0000")
            .send()
            .await
            .unwrap();

        assert_eq!(response.status(), 200);
    }
}
