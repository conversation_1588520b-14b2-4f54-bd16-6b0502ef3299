use git2::{
    BranchType, Direction, FetchOptions, PushOptions, RemoteCallbacks, Repository, Signature,
};

use axum_git_server::{
    config::load_config, error::AppError, helpers::sha::is_valid_sha1, helpers::temp::TempDir,
    types::repository::RepoIdentifier,
};

#[cfg(test)]
mod common;

use common::test_helpers::{
    clone_repo_to_temp, configure_git_user, create_branch_with_empty_commit_and_push,
    create_random_repo_identifier, create_test_repo_through_api,
};

#[tokio::test]
async fn test_git2_real_service_with_branch() -> Result<(), AppError> {
    let config = load_config();

    let repo_identifier = RepoIdentifier {
        owner: "owner".to_string(),
        repo: format!("repo-{}", uuid::Uuid::new_v4()),
    };
    let branch = "master";

    create_test_repo_through_api(&repo_identifier, branch).await;

    let repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    println!("Cloned repo to local temp folder");

    create_branch_with_empty_commit_and_push(&repo_path, branch, "whatever").await?;
    println!("Create branch with empty commit");

    let git2_client_temp_dir = TempDir::new("git2-client").await.expect("temp dir created");

    // Initialize a new repository
    let repo =
        Repository::init_bare(&git2_client_temp_dir.path).expect("Failed to init repository");
    println!("Initialized repository at {:?}", git2_client_temp_dir.path);

    // Configure remote to point to the real service
    let remote_url = format!(
        "{}/{}/{}.git",
        &config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo,
    );
    let mut remote = repo
        .remote("origin", &remote_url)
        .expect("Failed to create remote");
    println!("Configured remote: {}", remote_url);

    // Setup callbacks for progress reporting
    let mut callbacks = RemoteCallbacks::new();
    callbacks.transfer_progress(|stats| {
        println!(
            "Transfer progress: {}/{} objects ({} bytes)",
            stats.received_objects(),
            stats.total_objects(),
            stats.received_bytes()
        );
        true
    });
    callbacks.sideband_progress(|data| {
        println!("Remote message: {}", String::from_utf8_lossy(data));
        true
    });

    // Configure fetch options
    let mut fetch_opts = FetchOptions::new();
    fetch_opts.remote_callbacks(callbacks);
    fetch_opts.download_tags(git2::AutotagOption::All);

    remote
        .fetch(&[branch], Some(&mut fetch_opts), None)
        .expect("Failed to fetch");

    let fetch_head = repo
        .find_reference("FETCH_HEAD")
        .expect("Failed to find FETCH_HEAD");
    let fetch_head_commit = fetch_head
        .peel_to_commit()
        .expect("Failed to find commit in FETCH_HEAD");
    println!("Fetch head commit: {}", fetch_head_commit.id());

    // There is no local reference created for the remote branch
    // Need to get the commit from the remote reference
    let head_ref = repo
        // .find_reference(&format!("refs/heads/{}", &branch))
        .find_reference(&format!("refs/remotes/origin/{}", &branch))
        .expect("Failed to find master branch");

    let commit = head_ref.peel_to_commit().expect("Failed to find commit");
    println!(
        "Found head commit: {} ({})",
        commit.id(),
        commit.message().unwrap_or("no message")
    );
    Ok(())
}

#[tokio::test]
async fn test_git2_real_service_with_commit_oid() -> Result<(), AppError> {
    let config = load_config();

    let repo_identifier = RepoIdentifier {
        owner: "owner".to_string(),
        repo: format!("repo-{}", uuid::Uuid::new_v4()),
    };
    let branch = "master";

    create_test_repo_through_api(&repo_identifier, branch).await;

    let repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;

    let commit_oid =
        create_branch_with_empty_commit_and_push(&repo_path, branch, "whatever").await?;
    assert!(is_valid_sha1(&commit_oid), "Commit OID is a valid SHA1");
    // let refspec = format!("+{sha}:refs/remotes/origin/{sha}", sha = commit_oid);
    let refspec = format!("{}", commit_oid);

    let git2_client_temp_dir = TempDir::new("git2-client").await.expect("temp dir created");

    // Initialize a new repository
    let repo =
        Repository::init_bare(&git2_client_temp_dir.path).expect("Failed to init repository");
    println!("Initialized repository at {:?}", git2_client_temp_dir.path);

    // Configure remote to point to the real service
    let remote_url = format!(
        "{}/{}/{}.git",
        &config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo,
    );
    let mut remote = repo
        .remote("origin", &remote_url)
        .expect("Failed to create remote");
    println!("Configured remote: {}", remote_url);

    // Setup callbacks for progress reporting
    let mut callbacks = RemoteCallbacks::new();
    callbacks.transfer_progress(|stats| {
        println!(
            "Transfer progress: {}/{} objects ({} bytes)",
            stats.received_objects(),
            stats.total_objects(),
            stats.received_bytes()
        );
        true
    });
    callbacks.sideband_progress(|data| {
        println!("Remote message: {}", String::from_utf8_lossy(data));
        true
    });

    // Configure fetch options
    let mut fetch_opts = FetchOptions::new();
    fetch_opts.remote_callbacks(callbacks);
    fetch_opts.download_tags(git2::AutotagOption::All);

    remote
        .fetch(&[&refspec], Some(&mut fetch_opts), None)
        .expect("Failed to fetch");

    let fetch_head = repo
        .find_reference("FETCH_HEAD")
        .expect("Failed to find FETCH_HEAD");
    let fetch_head_commit = fetch_head
        .peel_to_commit()
        .expect("Failed to find commit in FETCH_HEAD");
    println!("Fetch head commit: {}", fetch_head_commit.id());

    println!(
        "Found head commit: {} ({})",
        fetch_head_commit.id(),
        fetch_head_commit.message().unwrap_or("no message")
    );
    Ok(())
}

// ============================================================================
// COMPREHENSIVE GIT2 CLIENT TEST SUITE
// ============================================================================

#[tokio::test]
async fn test_git2_clone_empty_repository() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create empty repository
    create_test_repo_through_api(&repo_identifier, "main").await;

    let temp_dir = TempDir::new("git2-clone-empty")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Attempt to clone empty repository - this may fail with git2
    let clone_result = Repository::clone(&remote_url, &clone_path);

    let repo = clone_result.expect("cloning repo should work");

    assert!(repo.is_empty().expect("Failed to check if repo is empty"));

    // Verify HEAD doesn't exist (unborn branch)
    let head_result = repo.head();
    assert!(head_result.is_err());
    if let Err(error) = head_result {
        assert_eq!(error.code(), git2::ErrorCode::UnbornBranch);
    }
    println!("Successfully cloned empty repository");

    Ok(())
}

#[tokio::test]
async fn test_git2_clone_with_commits() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository and add commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("git2-clone-commits")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Clone repository with commits
    let repo = Repository::clone(&remote_url, &clone_path).expect("Failed to clone repository");

    // Verify repository state
    assert!(!repo.is_empty().expect("Failed to check if repo is empty"));

    let head = repo.head().expect("Failed to get HEAD");
    assert_eq!(head.name().unwrap(), "refs/heads/main");

    let commit = head.peel_to_commit().expect("Failed to get commit");
    assert_eq!(commit.message().unwrap().trim(), "Initial commit");

    println!("Successfully cloned repository with commits");
    Ok(())
}

#[tokio::test]
async fn test_git2_fetch_multiple_branches() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with multiple branches
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;

    // Create main branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Main commit").await?;

    // Create feature branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "feature", "Feature commit").await?;

    // Create development branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "development", "Dev commit").await?;

    // Initialize new repository for testing
    let temp_dir = TempDir::new("git2-multi-branch")
        .await
        .expect("temp dir created");
    let repo = Repository::init_bare(&temp_dir.path).expect("Failed to init repository");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let mut remote = repo
        .remote("origin", &remote_url)
        .expect("Failed to create remote");

    // Fetch all branches
    let mut fetch_opts = FetchOptions::new();
    remote
        .fetch(
            &["refs/heads/*:refs/remotes/origin/*"],
            Some(&mut fetch_opts),
            None,
        )
        .expect("Failed to fetch branches");

    // Verify all branches were fetched
    let branches = repo
        .branches(Some(BranchType::Remote))
        .expect("Failed to list branches");
    let branch_names: Vec<String> = branches
        .map(|b| b.unwrap().0.name().unwrap().unwrap().to_string())
        .collect();

    assert!(branch_names.contains(&"origin/main".to_string()));
    assert!(branch_names.contains(&"origin/feature".to_string()));
    assert!(branch_names.contains(&"origin/development".to_string()));

    println!("Successfully fetched multiple branches: {:?}", branch_names);
    Ok(())
}

#[tokio::test]
async fn test_git2_push_new_branch() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with initial commit
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    // Clone repository for testing
    let temp_dir = TempDir::new("git2-push-branch")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let repo = Repository::clone(&remote_url, &clone_path).expect("Failed to clone repository");

    // Configure git user
    configure_git_user(&clone_path).await?;

    // Create new branch
    let head_commit = repo.head().unwrap().peel_to_commit().unwrap();
    let new_branch = repo
        .branch("new-feature", &head_commit, false)
        .expect("Failed to create branch");

    // Checkout new branch
    repo.set_head(new_branch.get().name().unwrap())
        .expect("Failed to set HEAD");
    repo.checkout_head(Some(git2::build::CheckoutBuilder::default().force()))
        .expect("Failed to checkout");

    // Create a commit
    let signature =
        Signature::now("Test User", "<EMAIL>").expect("Failed to create signature");

    let tree_id = repo.index().unwrap().write_tree().unwrap();
    let tree = repo.find_tree(tree_id).unwrap();
    let parent_commit = repo.head().unwrap().peel_to_commit().unwrap();

    repo.commit(
        Some("HEAD"),
        &signature,
        &signature,
        "New feature commit",
        &tree,
        &[&parent_commit],
    )
    .expect("Failed to create commit");

    // Push new branch
    let mut remote = repo.find_remote("origin").expect("Failed to find remote");
    let mut push_opts = PushOptions::new();

    remote
        .push(
            &["refs/heads/new-feature:refs/heads/new-feature"],
            Some(&mut push_opts),
        )
        .expect("Failed to push branch");

    println!("Successfully pushed new branch");
    Ok(())
}

#[tokio::test]
async fn test_git2_list_remote_references() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with multiple branches
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;

    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Main commit").await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "feature", "Feature commit").await?;

    let temp_dir = TempDir::new("git2-list-refs")
        .await
        .expect("temp dir created");
    let repo = Repository::init_bare(&temp_dir.path).expect("Failed to init repository");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let mut remote = repo
        .remote("origin", &remote_url)
        .expect("Failed to create remote");

    // Connect and list references
    remote.connect(Direction::Fetch).expect("Failed to connect");
    let refs = remote.list().expect("Failed to list references");

    let ref_names: Vec<String> = refs.iter().map(|r| r.name().to_string()).collect();

    // Verify expected references exist
    assert!(ref_names.iter().any(|name| name.contains("main")));
    assert!(ref_names.iter().any(|name| name.contains("feature")));
    assert!(ref_names.iter().any(|name| name == "HEAD"));

    println!("Remote references: {:?}", ref_names);
    Ok(())
}

#[tokio::test]
async fn test_git2_fetch_with_progress_callbacks() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("git2-progress")
        .await
        .expect("temp dir created");
    let repo = Repository::init_bare(&temp_dir.path).expect("Failed to init repository");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let mut remote = repo
        .remote("origin", &remote_url)
        .expect("Failed to create remote");

    // Track progress using Arc<Mutex<bool>> for shared state
    use std::sync::{Arc, Mutex};
    let progress_called = Arc::new(Mutex::new(false));
    let progress_called_clone = progress_called.clone();

    let mut callbacks = RemoteCallbacks::new();
    callbacks.transfer_progress(move |stats| {
        *progress_called_clone.lock().unwrap() = true;
        println!(
            "Progress: {}/{} objects",
            stats.received_objects(),
            stats.total_objects()
        );
        true
    });
    callbacks.sideband_progress(|data| {
        println!("Sideband: {}", String::from_utf8_lossy(data));
        true
    });

    let mut fetch_opts = FetchOptions::new();
    fetch_opts.remote_callbacks(callbacks);

    remote
        .fetch(&["main"], Some(&mut fetch_opts), None)
        .expect("Failed to fetch");

    // Verify callbacks were called
    assert!(
        *progress_called.lock().unwrap(),
        "Progress callback should have been called"
    );

    println!("Fetch with progress callbacks completed successfully");
    Ok(())
}

#[tokio::test]
async fn test_git2_error_handling_nonexistent_repo() -> Result<(), AppError> {
    let config = load_config();

    let temp_dir = TempDir::new("git2-error").await.expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!("{}/nonexistent/repo.git", config.get_full_host());

    // Attempt to clone non-existent repository
    let result = Repository::clone(&remote_url, &clone_path);

    // let repo = result.expect("Repo creation failed");
    // println!("result: {:?}", repo.is_empty());
    // println!("result: {:?}", repo.path());
    assert!(result.is_err());

    let err = match result {
        Ok(_) => panic!("Should fail"),
        Err(e) => e,
    };
    assert!(err.to_string().contains("unexpected http status code: 404"));

    Ok(())
}

#[tokio::test]
async fn test_git2_fetch_specific_commit() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    let commit_oid =
        create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Target commit").await?;

    let temp_dir = TempDir::new("git2-fetch-commit")
        .await
        .expect("temp dir created");
    let repo = Repository::init_bare(&temp_dir.path).expect("Failed to init repository");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let mut remote = repo
        .remote("origin", &remote_url)
        .expect("Failed to create remote");

    // Fetch specific commit
    let mut fetch_opts = FetchOptions::new();
    remote
        .fetch(&[&commit_oid], Some(&mut fetch_opts), None)
        .expect("Failed to fetch specific commit");

    // Verify the commit was fetched
    let fetch_head = repo
        .find_reference("FETCH_HEAD")
        .expect("Failed to find FETCH_HEAD");
    let fetched_commit = fetch_head
        .peel_to_commit()
        .expect("Failed to get commit from FETCH_HEAD");

    assert_eq!(fetched_commit.id().to_string(), commit_oid);
    println!("Successfully fetched specific commit: {}", commit_oid);
    Ok(())
}

#[tokio::test]
async fn test_git2_concurrent_operations() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Spawn multiple concurrent clone operations
    let mut handles = Vec::new();

    for i in 0..3 {
        let url = remote_url.clone();
        let handle = tokio::spawn(async move {
            let temp_dir = TempDir::new(&format!("git2-concurrent-{}", i))
                .await
                .expect("temp dir created");
            let clone_path = temp_dir.path.join("cloned-repo");

            let result = Repository::clone(&url, &clone_path);
            assert!(result.is_ok(), "Concurrent clone {} should succeed", i);

            let repo = result.unwrap();
            assert!(!repo.is_empty().expect("Failed to check if repo is empty"));

            println!("Concurrent clone {} completed successfully", i);
        });
        handles.push(handle);
    }

    // Wait for all operations to complete
    for handle in handles {
        handle.await.expect("Concurrent operation should complete");
    }

    println!("All concurrent operations completed successfully");
    Ok(())
}

#[tokio::test]
async fn test_git2_repository_info() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("git2-repo-info")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let repo = Repository::clone(&remote_url, &clone_path).expect("Failed to clone repository");

    // Test repository information
    assert!(!repo.is_empty().expect("Failed to check if repo is empty"));
    assert!(!repo.is_bare());
    assert!(repo.path().exists());
    assert!(repo.workdir().is_some());

    // Test HEAD information
    let head = repo.head().expect("Failed to get HEAD");
    assert!(head.is_branch());
    assert_eq!(head.shorthand().unwrap(), "main");

    // Test remote information
    let remotes = repo.remotes().expect("Failed to get remotes");
    assert_eq!(remotes.len(), 1);
    assert_eq!(remotes.get(0).unwrap(), "origin");

    let origin = repo
        .find_remote("origin")
        .expect("Failed to find origin remote");
    assert_eq!(origin.url().unwrap(), remote_url);

    println!("Repository information verified successfully");
    Ok(())
}
