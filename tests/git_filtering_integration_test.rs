use std::process::Command;
use tempfile::TempDir;

#[cfg(test)]
mod common;

use common::test_helpers::{
    create_random_repo_identifier, create_test_repo_through_api,
    create_branch_with_empty_commit_and_push, clone_repo_to_temp,
};

/// Test branch-specific clone to ensure only the specified branch is cloned
/// This test requires the server to be running on localhost:3000
#[tokio::test]
#[ignore] // Ignore by default since it requires server to be running
async fn test_branch_specific_clone() {
    let repo_id = create_random_repo_identifier();

    // Create a test repository with main branch
    create_test_repo_through_api(&repo_id, "main").await;

    // Clone the repo to create additional branches
    let repo_path = clone_repo_to_temp("http://localhost:3000", &repo_id).await.unwrap();

    // Create additional branches
    create_branch_with_empty_commit_and_push(&repo_path, "feature-branch", "Feature commit").await.unwrap();
    create_branch_with_empty_commit_and_push(&repo_path, "dev-branch", "Dev commit").await.unwrap();

    // Clone only the specific branch
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let clone_path = temp_dir.path().join("repo");

    let clone_result = Command::new("git")
        .args([
            "clone",
            "--single-branch",
            "--branch", "feature-branch",
            &format!("http://localhost:3000/{}/{}.git", repo_id.owner, repo_id.repo),
            clone_path.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone");

    if !clone_result.status.success() {
        panic!(
            "Git clone failed: {}",
            String::from_utf8_lossy(&clone_result.stderr)
        );
    }

    // Verify only the specified branch exists locally
    let branch_list = Command::new("git")
        .args(["branch", "-a"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to list branches");

    let branches = String::from_utf8_lossy(&branch_list.stdout);
    println!("Branches in cloned repo:\n{}", branches);

    // Should have the feature-branch checked out
    assert!(branches.contains("* feature-branch"));

    // Should have remote tracking branch
    assert!(branches.contains("remotes/origin/feature-branch"));

    // Should NOT have other branches locally
    assert!(!branches.contains("main") || branches.contains("remotes/origin/main"));
    assert!(!branches.contains("dev-branch") || branches.contains("remotes/origin/dev-branch"));

    println!("✅ Branch-specific clone test passed");
}

/// Test blobless clone (blob:none filter) - only trees and commits, no file contents
#[tokio::test]
async fn test_blobless_clone() {
    let repo_id = create_random_repo_identifier();
    
    // Create a test repository
    create_test_repo_through_api(&repo_id, "main").await;
    
    // Clone with blob:none filter
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let clone_path = temp_dir.path().join("repo");
    
    let clone_result = Command::new("git")
        .args([
            "clone",
            "--filter=blob:none",
            &format!("http://localhost:3000/{}/{}.git", repo_id.owner, repo_id.repo),
            clone_path.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone");
    
    if !clone_result.status.success() {
        let stderr = String::from_utf8_lossy(&clone_result.stderr);
        println!("Git clone stderr: {}", stderr);
        
        // If filtering isn't supported yet, skip this test
        if stderr.contains("does not support") || stderr.contains("unexpected line") {
            println!("⚠️  Blobless clone not yet supported, skipping test");
            return;
        }
        
        panic!("Git clone failed: {}", stderr);
    }
    
    // Verify the clone was successful
    assert!(clone_path.exists());
    assert!(clone_path.join(".git").exists());
    
    // Check that we have commit history but no blob contents
    let log_result = Command::new("git")
        .args(["log", "--oneline"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to get git log");
    
    assert!(log_result.status.success());
    let log_output = String::from_utf8_lossy(&log_result.stdout);
    assert!(!log_output.is_empty(), "Should have commit history");
    
    // Try to check out a file - this should trigger a fetch of the blob
    let ls_result = Command::new("git")
        .args(["ls-tree", "HEAD"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to list tree");
    
    if ls_result.status.success() {
        let tree_output = String::from_utf8_lossy(&ls_result.stdout);
        println!("Tree contents: {}", tree_output);
        
        // If there are files in the tree, try to access one
        if !tree_output.is_empty() {
            // Try to show a file - this might fail if blob is missing
            let show_result = Command::new("git")
                .args(["show", "HEAD:README.md"])
                .current_dir(&clone_path)
                .output();
            
            if let Ok(show_output) = show_result {
                if !show_output.status.success() {
                    let stderr = String::from_utf8_lossy(&show_output.stderr);
                    println!("Expected blob missing error: {}", stderr);
                    // This is expected for blobless clones
                }
            }
        }
    }
    
    println!("✅ Blobless clone test completed");
}

/// Test treeless clone (tree:0 filter) - only commits, no trees or blobs
#[tokio::test]
async fn test_treeless_clone() {
    let repo_id = create_random_repo_identifier();
    
    // Create a test repository
    create_test_repo_through_api(&repo_id, "main").await;
    
    // Clone with tree:0 filter
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let clone_path = temp_dir.path().join("repo");
    
    let clone_result = Command::new("git")
        .args([
            "clone",
            "--filter=tree:0",
            &format!("http://localhost:3000/{}/{}.git", repo_id.owner, repo_id.repo),
            clone_path.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone");
    
    if !clone_result.status.success() {
        let stderr = String::from_utf8_lossy(&clone_result.stderr);
        println!("Git clone stderr: {}", stderr);
        
        // If filtering isn't supported yet, skip this test
        if stderr.contains("does not support") || stderr.contains("unexpected line") {
            println!("⚠️  Treeless clone not yet supported, skipping test");
            return;
        }
        
        panic!("Git clone failed: {}", stderr);
    }
    
    // Verify the clone was successful
    assert!(clone_path.exists());
    assert!(clone_path.join(".git").exists());
    
    // Check that we have commit history
    let log_result = Command::new("git")
        .args(["log", "--oneline"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to get git log");
    
    assert!(log_result.status.success());
    let log_output = String::from_utf8_lossy(&log_result.stdout);
    assert!(!log_output.is_empty(), "Should have commit history");
    
    // Try to list tree - this should fail or be empty for treeless clone
    let ls_result = Command::new("git")
        .args(["ls-tree", "HEAD"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to list tree");
    
    if ls_result.status.success() {
        let tree_output = String::from_utf8_lossy(&ls_result.stdout);
        println!("Tree contents (should be empty): {}", tree_output);
        // For treeless clones, this should be empty or fail
    } else {
        let stderr = String::from_utf8_lossy(&ls_result.stderr);
        println!("Expected tree missing error: {}", stderr);
        // This is expected for treeless clones
    }
    
    println!("✅ Treeless clone test completed");
}

/// Test shallow clone with depth limit
#[tokio::test]
async fn test_shallow_clone() {
    let repo_id = create_random_repo_identifier();
    
    // Create a test repository
    create_test_repo_through_api(&repo_id, "main").await;
    
    // Clone the repo to create additional commits
    let repo_path = clone_repo_to_temp("http://localhost:3000", &repo_id).await.unwrap();

    // Create additional commits on main branch
    create_branch_with_empty_commit_and_push(&repo_path, "main", "Second commit").await.unwrap();
    create_branch_with_empty_commit_and_push(&repo_path, "main", "Third commit").await.unwrap();
    
    // Clone with depth limit
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let clone_path = temp_dir.path().join("repo");
    
    let clone_result = Command::new("git")
        .args([
            "clone",
            "--depth=1",
            &format!("http://localhost:3000/{}/{}.git", repo_id.owner, repo_id.repo),
            clone_path.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone");
    
    if !clone_result.status.success() {
        panic!(
            "Git shallow clone failed: {}",
            String::from_utf8_lossy(&clone_result.stderr)
        );
    }
    
    // Verify the clone was successful
    assert!(clone_path.exists());
    assert!(clone_path.join(".git").exists());
    
    // Check that we have limited commit history
    let log_result = Command::new("git")
        .args(["log", "--oneline"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to get git log");
    
    assert!(log_result.status.success());
    let log_output = String::from_utf8_lossy(&log_result.stdout);
    let commit_count = log_output.lines().count();
    
    println!("Commit count in shallow clone: {}", commit_count);
    println!("Commits:\n{}", log_output);
    
    // Should have only 1 commit due to --depth=1
    assert_eq!(commit_count, 1, "Shallow clone should have exactly 1 commit");
    
    // Check if it's a shallow repository
    let shallow_check = Command::new("git")
        .args(["rev-parse", "--is-shallow-repository"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to check if shallow");
    
    if shallow_check.status.success() {
        let output = String::from_utf8_lossy(&shallow_check.stdout);
        let is_shallow = output.trim();
        assert_eq!(is_shallow, "true", "Repository should be shallow");
    }
    
    println!("✅ Shallow clone test passed");
}

/// Test combined filter (blob:none + shallow)
#[tokio::test]
async fn test_combined_filter_clone() {
    let repo_id = create_random_repo_identifier();
    
    // Create a test repository
    create_test_repo_through_api(&repo_id, "main").await;
    
    // Clone with combined filters
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let clone_path = temp_dir.path().join("repo");
    
    let clone_result = Command::new("git")
        .args([
            "clone",
            "--depth=1",
            "--filter=blob:none",
            &format!("http://localhost:3000/{}/{}.git", repo_id.owner, repo_id.repo),
            clone_path.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone");
    
    if !clone_result.status.success() {
        let stderr = String::from_utf8_lossy(&clone_result.stderr);
        println!("Git combined filter clone stderr: {}", stderr);
        
        // If filtering isn't supported yet, skip this test
        if stderr.contains("does not support") || stderr.contains("unexpected line") {
            println!("⚠️  Combined filtering not yet supported, skipping test");
            return;
        }
        
        panic!("Git combined filter clone failed: {}", stderr);
    }
    
    // Verify the clone was successful
    assert!(clone_path.exists());
    assert!(clone_path.join(".git").exists());
    
    // Should be both shallow and blobless
    let log_result = Command::new("git")
        .args(["log", "--oneline"])
        .current_dir(&clone_path)
        .output()
        .expect("Failed to get git log");
    
    assert!(log_result.status.success());
    let log_output = String::from_utf8_lossy(&log_result.stdout);
    let commit_count = log_output.lines().count();
    
    // Should have limited commits due to depth
    assert!(commit_count <= 1, "Should have limited commit history");
    
    println!("✅ Combined filter clone test completed");
}

/// Helper function to check if git supports a specific filter
fn git_supports_filter(_filter: &str) -> bool {
    let result = Command::new("git")
        .args(["help", "clone"])
        .output();
    
    if let Ok(output) = result {
        let help_text = String::from_utf8_lossy(&output.stdout);
        help_text.contains("--filter")
    } else {
        false
    }
}

/// Test to verify git client supports filtering
#[test]
fn test_git_client_filter_support() {
    let supports_filter = git_supports_filter("blob:none");
    println!("Git client supports --filter: {}", supports_filter);
    
    if !supports_filter {
        println!("⚠️  Git client doesn't support filtering, some tests may be skipped");
    }
}
