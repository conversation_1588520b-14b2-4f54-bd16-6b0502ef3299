use axum_git_server::{config::load_config, helpers::sha::is_valid_sha1};
use gix::bstr::ByteVec;
use std::str::FromStr;

#[cfg(test)]
mod common;

use common::test_helpers::{
    clone_repo_to_temp, create_branch_with_empty_commit_and_push, create_random_repo_identifier,
    create_test_repo_through_api, parse_pkt_lines,
};

#[tokio::test]
async fn test_git_advertisement_with_wrong_service_type() {
    let config = load_config();
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let url = format!(
        "{}/{}/{}.git/info/refs?service=wrong",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );
    let client = reqwest::Client::new();
    let response = client.get(&url).send().await.unwrap();

    assert_eq!(response.status(), reqwest::StatusCode::OK);

    let body_bytes = response.bytes().await.expect("get bytes");
    let pkt_lines = parse_pkt_lines(&body_bytes);

    assert_eq!(pkt_lines.len(), 3);

    let first_line = &pkt_lines[0];
    assert!(first_line.starts_with(b"# service=wrong"));

    assert_eq!(&pkt_lines[1], &Vec::<u8>::new());
    assert_eq!(&pkt_lines[2], &Vec::<u8>::new());
}

#[tokio::test]
async fn test_git_advertisement_with_nonexistent_repository() {
    let config = load_config();
    let url = format!(
        "{}/{}/{}.git/info/refs?service=git-receive-pack",
        config.get_full_host(),
        "non-existent",
        "non-existent.git"
    );

    let client = reqwest::Client::new();
    let response = client.get(&url).send().await.expect("resonse");

    assert_eq!(response.status(), reqwest::StatusCode::NOT_FOUND);
}

// Helper functions for comprehensive testing
async fn make_advertisement_request(
    owner: &str,
    repo: &str,
    service: &str,
    protocol_version: Option<&str>,
) -> (reqwest::StatusCode, reqwest::header::HeaderMap, Vec<u8>) {
    let start = std::time::Instant::now();
    let config = load_config();
    println!("Loaded config in {:?}", start.elapsed());
    let url = format!(
        "{}/{}/{}.git/info/refs?service={}",
        config.get_full_host(),
        owner,
        repo,
        service
    );

    let mut headers = reqwest::header::HeaderMap::new();
    if let Some(version) = protocol_version {
        headers.insert(
            "Git-Protocol",
            reqwest::header::HeaderValue::from_str(version).unwrap(),
        );
    }

    // let client = reqwest::Client::new();
    let client = reqwest::Client::builder()
        .pool_max_idle_per_host(50) // Increase from default (~2)
        .pool_idle_timeout(std::time::Duration::from_secs(30))
        .build()
        .expect("Client created");

    let response = client.get(&url).headers(headers).send().await.unwrap();
    println!("Res arrived in {:?}", start.elapsed());

    let status = response.status();
    let response_headers = response.headers().clone();
    let body_bytes = response.bytes().await.unwrap().to_vec();
    println!("Cloned stuff in {:?}", start.elapsed());

    (status, response_headers, body_bytes)
}

fn validate_service_announcement(pkt_lines: &[Vec<u8>], expected_service: &str) {
    assert!(
        !pkt_lines.is_empty(),
        "Response should have at least one packet line"
    );

    let service_line = &pkt_lines[0];
    let service_str = String::from_utf8_lossy(service_line);
    assert!(
        service_str.starts_with(&format!("# service={}", expected_service)),
        "First line should be service announcement, got: {}",
        service_str
    );

    // Second line should be flush packet
    assert_eq!(
        &pkt_lines[1],
        &Vec::<u8>::new(),
        "Second line should be flush packet"
    );
}

fn validate_v1_advertisement(pkt_lines: &[Vec<u8>], service: &str, has_refs: bool) {
    validate_service_announcement(pkt_lines, service);

    if !has_refs {
        // Empty repository should have exactly 3 lines: service, flush, flush
        assert_eq!(
            pkt_lines.len(),
            3,
            "Empty repository should have 3 packet lines"
        );
        assert_eq!(
            &pkt_lines[2],
            &Vec::<u8>::new(),
            "Third line should be flush packet"
        );
        return;
    }

    // Should have at least: service, flush, HEAD ref, other refs..., flush
    assert!(
        pkt_lines.len() >= 4,
        "Repository with refs should have at least 4 packet lines"
    );

    // Find the first ref line (should contain capabilities)
    let mut found_ref_with_caps = false;
    for line in pkt_lines.iter().skip(2) {
        if line.is_empty() {
            break; // Hit flush packet
        }

        let line_str = String::from_utf8_lossy(line);
        if line_str.contains('\0') {
            // This is the first ref with capabilities
            found_ref_with_caps = true;

            // Validate SHA format (40 hex chars)
            let parts: Vec<&str> = line_str.split_whitespace().collect();
            assert!(!parts.is_empty(), "Ref line should have SHA");
            assert!(
                is_valid_sha1(parts[0]),
                "First part should be valid SHA1: {}",
                parts[0]
            );

            // Check for expected capabilities based on service
            let caps_part = line_str.split('\0').nth(1).unwrap_or("");
            match service {
                "git-upload-pack" => {
                    assert!(
                        caps_part.contains("multi_ack"),
                        "Should contain multi_ack capability"
                    );
                    assert!(
                        caps_part.contains("thin-pack"),
                        "Should contain thin-pack capability"
                    );
                    assert!(
                        caps_part.contains("side-band"),
                        "Should contain side-band capability"
                    );
                }
                "git-receive-pack" => {
                    assert!(
                        caps_part.contains("report-status"),
                        "Should contain report-status capability"
                    );
                    assert!(
                        caps_part.contains("delete-refs"),
                        "Should contain delete-refs capability"
                    );
                }
                _ => panic!("Unexpected service: {}", service),
            }
            break;
        }
    }

    assert!(
        found_ref_with_caps,
        "Should have found at least one ref line with capabilities"
    );

    // Last line should be flush packet
    assert_eq!(
        pkt_lines.last().unwrap(),
        &Vec::<u8>::new(),
        "Last line should be flush packet"
    );
}

fn validate_v2_advertisement(pkt_lines: &[Vec<u8>], service: &str) {
    validate_service_announcement(pkt_lines, service);

    // Should have: service, flush, "version 2", capabilities..., flush
    assert!(
        pkt_lines.len() >= 4,
        "V2 advertisement should have at least 4 packet lines"
    );

    // Third line should be "version 2"
    let version_line = String::from_utf8_lossy(&pkt_lines[2]);
    assert_eq!(
        version_line.trim(),
        "version 2",
        "Third line should be 'version 2'"
    );

    // Collect capabilities
    let mut capabilities = Vec::new();
    for line in pkt_lines.iter().skip(3) {
        if line.is_empty() {
            break; // Hit flush packet
        }
        capabilities.push(String::from_utf8_lossy(line).trim().to_string());
    }

    // Validate expected capabilities
    assert!(
        capabilities.iter().any(|cap| cap.starts_with("agent=")),
        "Should have agent capability"
    );

    match service {
        "git-upload-pack" => {
            assert!(
                capabilities.iter().any(|cap| cap.starts_with("ls-refs")),
                "Should have ls-refs capability"
            );
            assert!(
                capabilities.iter().any(|cap| cap.starts_with("fetch")),
                "Should have fetch capability"
            );
            assert!(
                capabilities.iter().any(|cap| cap == "object-format=sha1"),
                "Should have object-format capability"
            );
        }
        "git-receive-pack" => {
            assert!(
                capabilities.iter().any(|cap| cap == "push-options"),
                "Should have push-options capability"
            );
            assert!(
                capabilities.iter().any(|cap| cap == "atomic"),
                "Should have atomic capability"
            );
            assert!(
                capabilities.iter().any(|cap| cap == "report-status"),
                "Should have report-status capability"
            );
        }
        _ => panic!("Unexpected service: {}", service),
    }

    // Last line should be flush packet
    assert_eq!(
        pkt_lines.last().unwrap(),
        &Vec::<u8>::new(),
        "Last line should be flush packet"
    );
}

// Comprehensive test cases for all scenarios

#[tokio::test]
async fn test_git_upload_pack_v1_with_repository() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Create some commits to have refs
    let config = load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &identifier)
        .await
        .unwrap();
    create_branch_with_empty_commit_and_push(&clone_path, "feature-branch", "Test commit")
        .await
        .unwrap();

    let (status, headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // Default to v1
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    assert_eq!(
        headers.get(reqwest::header::CONTENT_TYPE).unwrap(),
        "application/x-git-upload-pack-advertisement"
    );
    assert_eq!(headers.get("Git-Protocol").unwrap(), "version=1");

    let pkt_lines = parse_pkt_lines(&body);
    validate_v1_advertisement(&pkt_lines, "git-upload-pack", true);
}

#[tokio::test]
async fn test_git_upload_pack_v2_with_repository() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    assert_eq!(
        headers.get(reqwest::header::CONTENT_TYPE).unwrap(),
        "application/x-git-upload-pack-advertisement"
    );
    assert_eq!(headers.get("Git-Protocol").unwrap(), "version=2");

    let pkt_lines = parse_pkt_lines(&body);
    validate_v2_advertisement(&pkt_lines, "git-upload-pack");
}

#[tokio::test]
async fn test_git_receive_pack_v1_with_repository() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-receive-pack",
        None, // Default to v1
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    assert_eq!(
        headers.get(reqwest::header::CONTENT_TYPE).unwrap(),
        "application/x-git-receive-pack-advertisement"
    );
    assert_eq!(headers.get("Git-Protocol").unwrap(), "version=1");

    let pkt_lines = parse_pkt_lines(&body);
    validate_v1_advertisement(&pkt_lines, "git-receive-pack", true);
}

#[tokio::test]
async fn test_git_receive_pack_v2_with_repository() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-receive-pack",
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    assert_eq!(
        headers.get(reqwest::header::CONTENT_TYPE).unwrap(),
        "application/x-git-receive-pack-advertisement"
    );
    assert_eq!(headers.get("Git-Protocol").unwrap(), "version=2");

    let pkt_lines = parse_pkt_lines(&body);
    validate_v2_advertisement(&pkt_lines, "git-receive-pack");
}

#[tokio::test]
async fn test_empty_repository_v1_upload_pack() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, _headers, body) =
        make_advertisement_request(&identifier.owner, &identifier.repo, "git-upload-pack", None)
            .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);
    validate_v1_advertisement(&pkt_lines, "git-upload-pack", true); // Even empty repos have HEAD
}

#[tokio::test]
async fn test_empty_repository_v2_upload_pack() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);
    validate_v2_advertisement(&pkt_lines, "git-upload-pack");
}

#[tokio::test]
async fn test_nonexistent_repository_v1() {
    let (status, headers, body) = make_advertisement_request(
        "nonexistent-owner",
        "nonexistent-repo",
        "git-upload-pack",
        None,
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::NOT_FOUND);
    assert_eq!(body.into_os_string_lossy(), "Repository not found\n");

    let content_type_header = headers
        .get(reqwest::header::CONTENT_TYPE)
        .expect("Content type header to be available")
        .to_str()
        .expect("Valid string");
    assert_eq!(content_type_header, "text/plain");
    let content_length_header = headers
        .get(reqwest::header::CONTENT_LENGTH)
        .expect("Content Length header to be available")
        .to_str()
        .expect("Valid string");
    assert_eq!(content_length_header, "21");
}

#[tokio::test]
async fn test_nonexistent_repository_v2() {
    let (status, headers, body) = make_advertisement_request(
        "nonexistent-owner",
        "nonexistent-repo",
        "git-upload-pack",
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::NOT_FOUND);
    assert_eq!(body.into_os_string_lossy(), "Repository not found\n");

    let content_type_header = headers
        .get(reqwest::header::CONTENT_TYPE)
        .expect("Content type header to be available")
        .to_str()
        .expect("Valid string");
    assert_eq!(content_type_header, "text/plain");
    let content_length_header = headers
        .get(reqwest::header::CONTENT_LENGTH)
        .expect("Content Length header to be available")
        .to_str()
        .expect("Valid string");
    assert_eq!(content_length_header, "21");
}

#[tokio::test]
async fn test_invalid_service_type() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, _headers, body) =
        make_advertisement_request(&identifier.owner, &identifier.repo, "invalid-service", None)
            .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Should return empty advertisement with the invalid service name
    assert_eq!(pkt_lines.len(), 3);
    let service_line = String::from_utf8_lossy(&pkt_lines[0]);
    assert!(service_line.starts_with("# service=invalid-service"));
    assert_eq!(&pkt_lines[1], &Vec::<u8>::new()); // Flush
    assert_eq!(&pkt_lines[2], &Vec::<u8>::new()); // Flush
}

#[tokio::test]
async fn test_protocol_version_header_variations() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Test various protocol version header values
    let test_cases = vec![
        (None, "version=1"),              // No header defaults to v1
        (Some("version=1"), "version=1"), // Explicit v1
        (Some("version=2"), "version=2"), // Explicit v2
        (Some("invalid"), "version=1"),   // Invalid defaults to v1
        (Some(""), "version=1"),          // Empty defaults to v1
    ];

    for (input_version, expected_version) in test_cases {
        let (status, headers, body) = make_advertisement_request(
            &identifier.owner,
            &identifier.repo,
            "git-upload-pack",
            input_version,
        )
        .await;

        assert_eq!(status, reqwest::StatusCode::OK);
        assert_eq!(headers.get("Git-Protocol").unwrap(), expected_version);

        let pkt_lines = parse_pkt_lines(&body);
        if expected_version == "version=2" {
            validate_v2_advertisement(&pkt_lines, "git-upload-pack");
        } else {
            validate_v1_advertisement(&pkt_lines, "git-upload-pack", true);
        }
    }
}

#[tokio::test]
async fn test_response_headers_comprehensive() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, headers, _body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);

    // Verify all expected headers
    assert_eq!(
        headers.get(reqwest::header::CONTENT_TYPE).unwrap(),
        "application/x-git-upload-pack-advertisement"
    );
    assert_eq!(headers.get("Git-Protocol").unwrap(), "version=2");
    assert_eq!(headers.get("Cache-Control").unwrap(), "no-cache");
    assert_eq!(headers.get("Pragma").unwrap(), "no-cache");
}

// Additional comprehensive tests for edge cases and robustness

#[tokio::test]
async fn test_user_agent_header_handling() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let config = load_config();
    let url = format!(
        "{}/{}/{}.git/info/refs?service=git-upload-pack",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );

    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("User-Agent", "git/2.39.0")
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), reqwest::StatusCode::OK);

    // Test with different User-Agent strings
    let user_agents = vec![
        "git/2.39.0",
        "git/2.34.1",
        "JGit/6.0.0",
        "libgit2 1.4.0",
        "custom-git-client/1.0",
    ];

    for user_agent in user_agents {
        let response = client
            .get(&url)
            .header("User-Agent", user_agent)
            .send()
            .await
            .unwrap();

        assert_eq!(response.status(), reqwest::StatusCode::OK);
    }
}

#[tokio::test]
async fn test_content_length_accuracy() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);

    if let Some(content_length_header) = headers.get(reqwest::header::CONTENT_LENGTH) {
        let content_length: usize = content_length_header.to_str().unwrap().parse().unwrap();

        assert_eq!(
            content_length,
            body.len(),
            "Content-Length header should match actual body size"
        );
    }
    // Note: Some servers don't set Content-Length for chunked responses, which is also valid
}

#[tokio::test]
async fn test_http_method_validation() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let config = load_config();
    let url = format!(
        "{}/{}/{}.git/info/refs?service=git-upload-pack",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );

    let client = reqwest::Client::new();

    // Test POST method (should not be allowed for advertisements)
    let response = client.post(&url).send().await.unwrap();
    assert!(
        response.status() == reqwest::StatusCode::METHOD_NOT_ALLOWED
            || response.status() == reqwest::StatusCode::NOT_FOUND
            || response.status().is_client_error(),
        "POST should not be allowed for advertisements, got: {}",
        response.status()
    );

    // Test PUT method
    let response = client.put(&url).send().await.unwrap();
    assert!(
        response.status() == reqwest::StatusCode::METHOD_NOT_ALLOWED
            || response.status() == reqwest::StatusCode::NOT_FOUND
            || response.status().is_client_error(),
        "PUT should not be allowed for advertisements, got: {}",
        response.status()
    );

    // Test DELETE method
    let response = client.delete(&url).send().await.unwrap();
    assert!(
        response.status() == reqwest::StatusCode::METHOD_NOT_ALLOWED
            || response.status() == reqwest::StatusCode::NOT_FOUND
            || response.status().is_client_error(),
        "DELETE should not be allowed for advertisements, got: {}",
        response.status()
    );

    // GET should work
    let response = client.get(&url).send().await.unwrap();
    assert_eq!(response.status(), reqwest::StatusCode::OK);
}

#[tokio::test]
async fn test_query_parameter_edge_cases() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let config = load_config();
    let base_url = format!(
        "{}/{}/{}.git/info/refs",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );

    let client = reqwest::Client::new();

    // Test missing service parameter
    let response = client.get(&base_url).send().await.unwrap();
    // Some servers return 200 with empty response, others return 4xx/5xx - both are valid
    assert!(
        response.status() == reqwest::StatusCode::OK
            || response.status().is_client_error()
            || response.status().is_server_error(),
        "Missing service parameter should be handled gracefully, got: {}",
        response.status()
    );

    // Test empty service parameter
    let url_empty_service = format!("{}?service=", base_url);
    let response = client.get(&url_empty_service).send().await.unwrap();
    assert!(
        response.status() == reqwest::StatusCode::OK || response.status().is_client_error(),
        "Empty service parameter should be handled gracefully, got: {}",
        response.status()
    );

    // Test multiple service parameters (should use first one)
    let url_multiple_service = format!(
        "{}?service=git-upload-pack&service=git-receive-pack",
        base_url
    );
    let response = client.get(&url_multiple_service).send().await.unwrap();
    assert_eq!(response.status(), reqwest::StatusCode::OK);

    // Test service parameter with extra whitespace
    let url_whitespace_service = format!("{}?service= git-upload-pack ", base_url);
    let response = client.get(&url_whitespace_service).send().await.unwrap();
    // Should either work or return error gracefully
    assert!(
        response.status() == reqwest::StatusCode::OK || response.status().is_client_error(),
        "Service with whitespace should be handled gracefully, got: {}",
        response.status()
    );

    // Test additional query parameters (should be ignored)
    let url_extra_params = format!(
        "{}?service=git-upload-pack&extra=value&another=param",
        base_url
    );
    let response = client.get(&url_extra_params).send().await.unwrap();
    assert_eq!(response.status(), reqwest::StatusCode::OK);
}

#[tokio::test]
async fn test_special_characters_in_repo_names() {
    let config = load_config();
    let client = reqwest::Client::new();

    let test_cases = vec![
        ("owner-with-dash", "repo-with-dash"),
        ("owner_with_underscore", "repo_with_underscore"),
        ("owner123", "repo456"),
        ("OwnerWithCaps", "RepoWithCaps"),
    ];

    for (owner, repo) in test_cases {
        let url = format!(
            "{}/{}/{}.git/info/refs?service=git-upload-pack",
            config.get_full_host(),
            owner,
            repo
        );

        let response = client.get(&url).send().await.unwrap();

        // Should handle gracefully (either 200 with empty advertisement or 404)
        assert!(
            response.status() == reqwest::StatusCode::OK
                || response.status() == reqwest::StatusCode::NOT_FOUND,
            "Special characters in repo names should be handled gracefully for {}/{}, got: {}",
            owner,
            repo,
            response.status()
        );

        if response.status() == reqwest::StatusCode::OK {
            let body = response.bytes().await.unwrap();
            let pkt_lines = parse_pkt_lines(&body);
            // Should at least have service announcement and flush
            assert!(pkt_lines.len() >= 2);
        }
    }
}

#[tokio::test]
async fn test_concurrent_advertisement_requests() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let mut handles = vec![];

    // Launch 10 concurrent requests
    for i in 0..10 {
        let owner = identifier.owner.clone();
        let repo = identifier.repo.clone();
        let service = if i % 2 == 0 {
            "git-upload-pack"
        } else {
            "git-receive-pack"
        };
        let protocol = if i % 3 == 0 { Some("version=2") } else { None };

        let handle = tokio::spawn(async move {
            make_advertisement_request(&owner, &repo, service, protocol).await
        });
        handles.push(handle);
    }

    // Wait for all requests to complete
    for (i, handle) in handles.into_iter().enumerate() {
        let (status, _headers, body) = handle.await.unwrap();
        assert_eq!(status, reqwest::StatusCode::OK, "Request {} failed", i);

        let pkt_lines = parse_pkt_lines(&body);
        assert!(
            pkt_lines.len() >= 3,
            "Request {} returned invalid response",
            i
        );
    }
}

#[tokio::test]
async fn test_git_protocol_header_case_sensitivity() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let config = load_config();
    let url = format!(
        "{}/{}/{}.git/info/refs?service=git-upload-pack",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );

    let client = reqwest::Client::new();

    let test_cases = vec![
        "Git-Protocol",
        "git-protocol",
        "GIT-PROTOCOL",
        "Git-protocol",
    ];

    for header_name in test_cases {
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(
            reqwest::header::HeaderName::from_str(header_name).unwrap(),
            reqwest::header::HeaderValue::from_static("version=2"),
        );

        let response = client.get(&url).headers(headers).send().await.unwrap();
        assert_eq!(
            response.status(),
            reqwest::StatusCode::OK,
            "Header case {} should work",
            header_name
        );

        // Verify the response indicates v2 protocol
        let response_headers = response.headers();
        if let Some(git_protocol) = response_headers.get("Git-Protocol") {
            assert_eq!(git_protocol, "version=2");
        }
    }
}

#[tokio::test]
async fn test_advertisement_response_time() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let start = std::time::Instant::now();

    let (status, headers, _body) =
        make_advertisement_request(&identifier.owner, &identifier.repo, "git-upload-pack", None)
            .await;

    let duration = start.elapsed();

    println!("Headers: {:?}", headers);
    assert_eq!(status, reqwest::StatusCode::OK);
    // This keeps failing for 1 second. Performance issues will be handled later.
    assert!(
        duration < std::time::Duration::from_millis(2000),
        "Advertisement should respond quickly, took: {:?}",
        duration
    );
}

fn validate_packet_line_sizes(pkt_lines: &[Vec<u8>]) {
    for (i, line) in pkt_lines.iter().enumerate() {
        if !line.is_empty() {
            // Each packet-line should be <= 65516 bytes (0xFFFC)
            // This is the maximum size for a Git packet-line
            assert!(
                line.len() <= 65516,
                "Packet line {} too long: {} bytes (max 65516)",
                i,
                line.len()
            );
        }
    }
}

#[tokio::test]
async fn test_packet_line_size_limits() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Test both protocols
    for protocol in [None, Some("version=2")] {
        let (status, _headers, body) = make_advertisement_request(
            &identifier.owner,
            &identifier.repo,
            "git-upload-pack",
            protocol,
        )
        .await;

        assert_eq!(status, reqwest::StatusCode::OK);
        let pkt_lines = parse_pkt_lines(&body);
        validate_packet_line_sizes(&pkt_lines);
    }
}

#[tokio::test]
async fn test_large_repository_advertisement() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Create multiple branches to simulate a larger repository
    let config = load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &identifier)
        .await
        .unwrap();

    // First, create a commit on main to establish the branch
    create_branch_with_empty_commit_and_push(&clone_path, "main", "Initial commit on main")
        .await
        .unwrap();

    // Create multiple feature branches (reasonable number for testing without being too slow)
    for i in 0..5 {
        create_branch_with_empty_commit_and_push(
            &clone_path,
            &format!("feature-branch-{}", i),
            &format!("Test commit {}", i),
        )
        .await
        .unwrap();
    }

    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // Use v1 to get ref listing
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Should have: service, flush, HEAD + capabilities, branch refs, flush
    // At minimum: service (1) + flush (1) + HEAD with caps (1) + flush (1) = 4
    // With branches: service (1) + flush (1) + HEAD with caps (1) + branch refs (5+) + flush (1) = 8+
    assert!(
        pkt_lines.len() >= 4,
        "Should have at least basic structure, got {} packet lines",
        pkt_lines.len()
    );

    // Validate packet-line sizes
    validate_packet_line_sizes(&pkt_lines);

    // Validate structure
    validate_v1_advertisement(&pkt_lines, "git-upload-pack", true);

    // Log the packet lines for debugging
    println!(
        "Large repo advertisement has {} packet lines",
        pkt_lines.len()
    );
    for (i, line) in pkt_lines.iter().enumerate() {
        if line.is_empty() {
            println!("  {}: FLUSH", i);
        } else {
            println!("  {}: {}", i, String::from_utf8_lossy(line));
        }
    }
}

#[tokio::test]
async fn test_missing_git_extension_in_url() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let config = load_config();

    // Test without .git extension
    let url_without_git = format!(
        "{}/{}/{}/info/refs?service=git-upload-pack",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );

    let client = reqwest::Client::new();
    let response = client.get(&url_without_git).send().await.unwrap();

    // Should either work or return 404 - both are acceptable
    assert!(
        response.status() == reqwest::StatusCode::OK
            || response.status() == reqwest::StatusCode::NOT_FOUND,
        "URL without .git extension should be handled gracefully, got: {}",
        response.status()
    );
}

#[tokio::test]
async fn test_malformed_repository_paths() {
    let config = load_config();
    let client = reqwest::Client::new();

    let malformed_paths = vec![
        "//double-slash/repo.git/info/refs?service=git-upload-pack",
        "/owner//double-slash.git/info/refs?service=git-upload-pack",
        "/owner/repo.git//info/refs?service=git-upload-pack",
        "/owner/repo.git/info//refs?service=git-upload-pack",
        "/../owner/repo.git/info/refs?service=git-upload-pack",
        "/owner/../repo.git/info/refs?service=git-upload-pack",
    ];

    for path in malformed_paths {
        let url = format!("{}{}", config.get_full_host(), path);
        let response = client.get(&url).send().await.unwrap();

        // Should handle malformed paths gracefully (not crash)
        assert!(
            response.status().is_client_error()
                || response.status() == reqwest::StatusCode::OK
                || response.status() == reqwest::StatusCode::NOT_FOUND,
            "Malformed path {} should be handled gracefully, got: {}",
            path,
            response.status()
        );
    }
}

#[tokio::test]
async fn test_advertisement_idempotency() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Make the same request multiple times
    let mut responses = Vec::new();
    for _ in 0..5 {
        let (status, _headers, body) = make_advertisement_request(
            &identifier.owner,
            &identifier.repo,
            "git-upload-pack",
            Some("version=2"),
        )
        .await;

        assert_eq!(status, reqwest::StatusCode::OK);
        responses.push(body);
    }

    // All responses should be identical (idempotent)
    let first_response = &responses[0];
    for (i, response) in responses.iter().enumerate().skip(1) {
        assert_eq!(
            response,
            first_response,
            "Response {} should be identical to first response",
            i + 1
        );
    }
}

// Tests for symref behavior and HEAD reference handling

#[tokio::test]
async fn test_symref_capability_with_main_branch() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // v1 protocol
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Find the capabilities line (should be the first ref line with \0)
    let mut found_symref = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue; // Skip flush packets
        }

        let line_str = String::from_utf8_lossy(line);
        if line_str.contains('\0') {
            // This is the capabilities line
            let caps_part = line_str.split('\0').nth(1).unwrap_or("");
            println!("Capabilities: {}", caps_part);

            // Should contain symref=HEAD:refs/heads/main
            assert!(
                caps_part.contains("symref=HEAD:refs/heads/main"),
                "Should contain symref=HEAD:refs/heads/main, got: {}",
                caps_part
            );
            found_symref = true;
            break;
        }
    }

    assert!(
        found_symref,
        "Should have found capabilities line with symref"
    );
}

#[tokio::test]
async fn test_symref_capability_with_master_branch() {
    let identifier = create_random_repo_identifier();
    let branch = "master";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // v1 protocol
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Find the capabilities line
    let mut found_symref = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }

        let line_str = String::from_utf8_lossy(line);
        if line_str.contains('\0') {
            let caps_part = line_str.split('\0').nth(1).unwrap_or("");
            println!("Capabilities: {}", caps_part);

            // Should contain symref=HEAD:refs/heads/master
            assert!(
                caps_part.contains("symref=HEAD:refs/heads/master"),
                "Should contain symref=HEAD:refs/heads/master, got: {}",
                caps_part
            );
            found_symref = true;
            break;
        }
    }

    assert!(
        found_symref,
        "Should have found capabilities line with symref"
    );
}

#[tokio::test]
async fn test_symref_capability_with_custom_branch() {
    let identifier = create_random_repo_identifier();
    let branch = "develop";
    create_test_repo_through_api(&identifier, branch).await;

    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // v1 protocol
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Find the capabilities line
    let mut found_symref = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }

        let line_str = String::from_utf8_lossy(line);
        if line_str.contains('\0') {
            let caps_part = line_str.split('\0').nth(1).unwrap_or("");
            println!("Capabilities: {}", caps_part);

            // Should contain symref=HEAD:refs/heads/develop
            assert!(
                caps_part.contains("symref=HEAD:refs/heads/develop"),
                "Should contain symref=HEAD:refs/heads/develop, got: {}",
                caps_part
            );
            found_symref = true;
            break;
        }
    }

    assert!(
        found_symref,
        "Should have found capabilities line with symref"
    );
}

#[tokio::test]
async fn test_unborn_head_handling() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Test immediately after creation (before any commits)
    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // v1 protocol
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Should have at least: service, flush, capabilities^{}, flush
    assert!(pkt_lines.len() >= 3, "Should have basic packet structure");

    // Find the capabilities line
    let mut found_capabilities = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }

        let line_str = String::from_utf8_lossy(line);
        if line_str.contains("capabilities^{}") || line_str.contains('\0') {
            println!("Capabilities line: {}", line_str);

            // Should still contain symref even for unborn HEAD
            assert!(
                line_str.contains("symref=HEAD:refs/heads/main"),
                "Should contain symref even for unborn HEAD, got: {}",
                line_str
            );
            found_capabilities = true;
            break;
        }
    }

    assert!(found_capabilities, "Should have found capabilities line");
}

#[tokio::test]
async fn test_head_resolution_after_commit() {
    let identifier = create_random_repo_identifier();
    let branch = "main";
    create_test_repo_through_api(&identifier, branch).await;

    // Create a commit to make the branch exist
    let config = load_config();
    let clone_path = clone_repo_to_temp(&config.get_full_host(), &identifier)
        .await
        .unwrap();
    let commit_sha =
        create_branch_with_empty_commit_and_push(&clone_path, branch, "Initial commit")
            .await
            .unwrap();

    // Now test the advertisement
    let (status, _headers, body) = make_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        "git-upload-pack",
        None, // v1 protocol
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    // Should now have actual HEAD with commit SHA
    let mut found_head_with_commit = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }

        let line_str = String::from_utf8_lossy(line);
        if line_str.contains("HEAD") && line_str.contains('\0') {
            println!("HEAD line: {}", line_str);

            // Should contain the actual commit SHA, not all zeros
            assert!(
                !line_str.starts_with("0000000000000000000000000000000000000000"),
                "HEAD should have real commit SHA, not zeros: {}",
                line_str
            );

            // Should contain symref capability
            assert!(
                line_str.contains("symref=HEAD:refs/heads/main"),
                "Should contain symref capability: {}",
                line_str
            );

            // Verify the SHA matches what we expect
            let sha_part = line_str.split_whitespace().next().unwrap();
            assert_eq!(
                sha_part.trim(),
                commit_sha.trim(),
                "HEAD SHA should match commit SHA"
            );

            found_head_with_commit = true;
            break;
        }
    }

    assert!(
        found_head_with_commit,
        "Should have found HEAD line with actual commit"
    );
}
