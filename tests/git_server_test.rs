use axum_git_server::{config::load_config, error::AppError};
use axum_git_server::{helpers::sha::is_valid_sha1, internal::command::run_command};

#[cfg(test)]
mod common;

use common::test_helpers::{
    clone_repo_to_temp, create_branch_with_empty_commit_and_push, create_random_repo_identifier,
    create_test_repo_through_api, create_want_request_headers, create_want_request_url,
    generate_random_sha1, not_our_ref_res_body,
};

//  curl -X POST -H "Content-Type: application/x-git-upload-pack-request" \
//    -H "User-Agent: git/2.47.0" -H "Git_protocol: version=2" \
//    --data "$(echo "0032want 921ae3e93a6eac8e1b4f652416520c5a5c58d1011\n0000")" \
//    'http://localhost:5500/owner/myrepo.git/git-upload-pack'
#[tokio::test]
async fn test_want_sha_request_v1() -> Result<(), AppError> {
    let config = load_config();

    let identifier = create_random_repo_identifier();
    let branch = "main";

    create_test_repo_through_api(&identifier, branch).await;

    let repo_path = clone_repo_to_temp(&config.get_full_host(), &identifier).await?;

    let commit_oid =
        create_branch_with_empty_commit_and_push(&repo_path, branch, "nothing").await?;
    assert!(is_valid_sha1(&commit_oid), "Commit OID is a valid SHA1");

    let headers = create_want_request_headers("version=1");
    // let mut headers = HeaderMap::new();
    // headers.insert(
    // "Content-Type",
    // HeaderValue::from_static("application/x-git-upload-pack-request"),
    // );
    // headers.insert("User-Agent", HeaderValue::from_static("git/2.47.0"));
    // setting this to v2 fails the test
    // this is because of incorrect implementation, since packfile requests should fallback to v1
    // will need to duplicate this test to ensure that it works the same for both v1 and v2 versions
    // headers.insert("Git-Protocol", HeaderValue::from_static("version=2"));

    let body = format!("0032want {}\n0000", commit_oid);
    // println!("SENT BODY: {}", body);

    let client = reqwest::Client::new();

    let url = format!(
        "{}/{}/{}.git/git-upload-pack",
        config.get_full_host(),
        identifier.owner,
        identifier.repo
    );
    let response = client
        .post(&url)
        .body(body)
        .headers(headers.clone())
        .send()
        .await
        .expect("Response to work");

    let status = response.status();
    assert_eq!(status, reqwest::StatusCode::OK);

    let text = response.text().await.expect("body available");
    assert_eq!(text, "");
    Ok(())
}

#[tokio::test]
async fn test_want_sha_with_done_packetline_request_v1() -> Result<(), AppError> {
    let config = load_config();

    let identifier = create_random_repo_identifier();

    let branch = "main";

    create_test_repo_through_api(&identifier, branch).await;

    let repo_path = clone_repo_to_temp(&config.get_full_host(), &identifier).await?;

    let commit_oid =
        create_branch_with_empty_commit_and_push(&repo_path, branch, "nothing").await?;
    assert!(is_valid_sha1(&commit_oid), "Commit OID is a valid SHA1");

    let client = reqwest::Client::new();
    let headers = create_want_request_headers("version=1");
    let url = create_want_request_url(config, identifier);
    let body = format!("0032want {}\n00000009done\n0000", commit_oid);

    let response = client
        .post(&url)
        .body(body)
        .headers(headers)
        .send()
        .await
        .expect("response to work");

    assert_eq!(response.status(), reqwest::StatusCode::OK);
    assert_eq!(
        response.text().await.expect("res body unwrapped"),
        "0008NAK\n"
    );

    Ok(())
}

#[tokio::test]
async fn test_want_sha_with_nonexistent_commit_request_v1() -> Result<(), AppError> {
    let config = load_config();

    let identifier = create_random_repo_identifier();
    let branch = "main";

    create_test_repo_through_api(&identifier, branch).await;

    clone_repo_to_temp(&config.get_full_host(), &identifier).await?;

    let commit_oid = generate_random_sha1();
    assert!(is_valid_sha1(&commit_oid), "Commit OID is a valid SHA1");

    let client = reqwest::Client::new();
    let headers = create_want_request_headers("version=1");
    let url = create_want_request_url(config, identifier);
    let body = format!("0032want {}\n00000009done\n0000", commit_oid);

    let response = client
        .post(&url)
        .body(body)
        .headers(headers)
        .send()
        .await
        .expect("response to work");

    assert_eq!(response.status(), reqwest::StatusCode::OK);
    assert_eq!(
        response.text().await.expect("res body unwrapped"),
        not_our_ref_res_body(&commit_oid)
    );

    Ok(())
}

#[tokio::test]
async fn test_want_sha_with_wrong_body_request_v1() -> Result<(), AppError> {
    let config = load_config();

    let identifier = create_random_repo_identifier();

    let branch = "main";

    create_test_repo_through_api(&identifier, branch).await;

    let client = reqwest::Client::new();
    let headers = create_want_request_headers("version=1");
    let url = create_want_request_url(config, identifier);
    let body = "foobar".to_string();

    let wrong_body_response = client
        .post(&url)
        .body(body)
        .headers(headers.clone())
        .send()
        .await
        .unwrap();

    let wrong_body_response_status = wrong_body_response.status();
    assert_eq!(
        wrong_body_response_status,
        reqwest::StatusCode::INTERNAL_SERVER_ERROR
    );

    // assert_eq!(
    // wrong_body_response.text().await.expect("body available"),
    // "TODO: define a proper error body",
    // );
    Ok(())
}

#[tokio::test]
async fn test_full_git_workflow() -> Result<(), AppError> {
    let config = load_config();

    let identifier = create_random_repo_identifier();
    let branch = "whatever";

    create_test_repo_through_api(&identifier, branch).await;

    let repo_path = clone_repo_to_temp(&config.get_full_host(), &identifier).await?;

    println!("Client repository path: {:?}", &repo_path);

    // Create and push a test commit
    let test_file_path = repo_path.join("test.txt");
    tokio::fs::write(&test_file_path, "test content")
        .await
        .expect("Failed to create test file");

    // Add and commit the test fil
    run_command("git", &["add", "test.txt"], Some(&repo_path), None).await?;

    run_command(
        "git",
        &["commit", "-m", "Add test file"],
        Some(&repo_path),
        None,
    )
    .await?;

    run_command(
        "git",
        &["push", "--set-upstream", "origin"],
        Some(&repo_path),
        None,
    )
    .await?;
    Ok(())
}

#[tokio::test]
async fn test_concurrent_cloning() -> Result<(), AppError> {
    let identifier = create_random_repo_identifier();

    let branch = "main";

    create_test_repo_through_api(&identifier, branch).await;

    // Create multiple temporary directories for concurrent clones
    let temp_dirs: Vec<_> = (0..5).map(|_| tempfile::tempdir().unwrap()).collect();

    // Perform concurrent clones
    let clone_tasks: Vec<_> = (0..temp_dirs.len())
        .map(|i| {
            let owner = identifier.owner.clone();
            let repo = identifier.repo.clone();
            let temp_dir = temp_dirs[i].path().to_owned();

            tokio::spawn(async move {
                let config = load_config();
                run_command(
                    "git",
                    &[
                        "clone",
                        &format!("{}/{}/{}.git", config.get_full_host(), owner, repo),
                    ],
                    Some(&temp_dir),
                    None,
                )
                .await?;
                println!("Finished clone {} in {:?}", i, temp_dir);
                Ok::<_, AppError>(())
            })
        })
        .collect();

    // Wait for all clones to complete
    let results = futures::future::join_all(clone_tasks).await;

    // Check results
    for (i, result) in results.into_iter().enumerate() {
        match result {
            Ok(_) => println!("Clone {} completed successfully", i),
            Err(e) => panic!("Clone {} failed: {}", i, e),
        }
    }
    Ok(())
}
