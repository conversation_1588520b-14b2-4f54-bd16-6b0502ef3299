use reqwest::header::{HeaderMap, HeaderValue};
use std::path::PathBuf;
use tempfile::TempDir;
use tokio::process::Command;

type TestResult = Result<(), Box<dyn std::error::Error + Send + Sync>>;

#[cfg(test)]
mod common;

use common::test_helpers::{
    configure_git_user, create_random_repo_identifier, create_test_repo_through_api,
    parse_pkt_lines,
};

use axum_git_server::{config::load_config, types::repository::RepoIdentifier};

/// Test suite for git-receive-pack functionality
///
/// This module tests the complete git-receive-pack (push) protocol implementation,
/// including advertisement, reference updates, pack-file processing, and hooks.

#[tokio::test]
async fn test_receive_pack_advertisement_v1() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let (status, headers, body) = make_receive_pack_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        None, // v1
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    assert_eq!(
        headers.get("content-type").unwrap(),
        "application/x-git-receive-pack-advertisement"
    );

    // Validate packet-line structure
    let pkt_lines = parse_pkt_lines(&body);
    validate_v1_receive_pack_advertisement(&pkt_lines);
}

#[tokio::test]
async fn test_receive_pack_advertisement_v2() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let (status, headers, body) = make_receive_pack_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        Some("version=2"),
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    assert_eq!(
        headers.get("content-type").unwrap(),
        "application/x-git-receive-pack-advertisement"
    );

    let pkt_lines = parse_pkt_lines(&body);
    validate_v2_receive_pack_advertisement(&pkt_lines);
}

#[tokio::test]
async fn test_simple_push_new_branch() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    // Clone repository
    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create and push a new branch
    create_and_push_branch(&clone_path, "feature-branch", "Test commit")
        .await
        .unwrap();

    // Verify the branch exists on the server
    verify_branch_exists(&identifier, "feature-branch").await;
}

#[tokio::test]
async fn test_push_to_existing_branch() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create initial commit on main
    create_and_push_branch(&clone_path, "main", "Initial commit")
        .await
        .unwrap();

    // Push another commit to main
    create_and_push_commit(&clone_path, "Second commit")
        .await
        .unwrap();

    // Verify both commits exist
    verify_commit_count(&identifier, "main", 2).await;
}

#[tokio::test]
async fn test_force_push() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create initial commits
    create_and_push_branch(&clone_path, "main", "Initial commit")
        .await
        .unwrap();
    create_and_push_commit(&clone_path, "Second commit")
        .await
        .unwrap();

    // Reset to first commit and force push
    reset_to_previous_commit(&clone_path).await.unwrap();
    force_push_branch(&clone_path, "main").await.unwrap();

    // Verify force push worked
    verify_commit_count(&identifier, "main", 1).await;
}

#[tokio::test]
async fn test_delete_branch() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create and push branches
    create_and_push_branch(&clone_path, "main", "Initial commit")
        .await
        .unwrap();
    create_and_push_branch(&clone_path, "feature-branch", "Feature commit")
        .await
        .unwrap();

    // Delete the feature branch
    delete_remote_branch(&clone_path, "feature-branch")
        .await
        .unwrap();

    // Verify branch is deleted
    verify_branch_not_exists(&identifier, "feature-branch").await;
}

#[tokio::test]
async fn test_push_multiple_branches() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create and push multiple branches
    let branches = vec!["main", "feature-1", "feature-2", "hotfix"];
    for branch in &branches {
        create_and_push_branch(&clone_path, branch, &format!("Commit for {}", branch))
            .await
            .unwrap();
    }

    // Verify all branches exist
    for branch in &branches {
        verify_branch_exists(&identifier, branch).await;
    }
}

#[tokio::test]
async fn test_push_with_tags() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create commit and tag
    create_and_push_branch(&clone_path, "main", "Initial commit")
        .await
        .unwrap();
    create_and_push_tag(&clone_path, "v1.0.0", "Release v1.0.0")
        .await
        .unwrap();

    // Verify tag exists
    verify_tag_exists(&identifier, "v1.0.0").await;
}

#[tokio::test]
async fn test_push_rejection_scenarios() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create initial commit and push
    create_and_push_branch(&clone_path, "main", "Initial commit")
        .await
        .unwrap();

    // Create a second commit to have something to reset to
    create_and_push_commit(&clone_path, "Second commit")
        .await
        .unwrap();

    // Now reset to previous commit and create a conflicting commit
    reset_to_previous_commit(&clone_path).await.unwrap();

    // Create a file to make a conflicting commit
    create_test_file(&clone_path, "conflict.txt", "conflicting content")
        .await
        .unwrap();
    add_and_commit(&clone_path, "Conflicting commit")
        .await
        .unwrap();

    // Try to push non-fast-forward update (should be rejected)
    let result = push_branch(&clone_path, "main").await;
    assert!(result.is_err(), "Non-fast-forward push should be rejected");
}

#[tokio::test]
async fn test_large_pack_file() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();
    let temp_dir = TempDir::new().unwrap();
    let clone_path = temp_dir.path().join("repo");

    clone_repository(&config.get_full_host(), &identifier, &clone_path)
        .await
        .unwrap();

    // Create a large file and push it
    create_large_file(&clone_path, "large_file.txt", 1024 * 1024)
        .await
        .unwrap(); // 1MB
    add_and_commit(&clone_path, "Add large file").await.unwrap();
    push_branch(&clone_path, "main").await.unwrap();

    // Verify large file was pushed successfully
    verify_file_exists(&identifier, "main", "large_file.txt").await;
}

#[tokio::test]
async fn test_concurrent_pushes() {
    let identifier = create_random_repo_identifier();
    create_test_repo_through_api(&identifier, "main").await;

    let config = load_config();

    // First, establish the main branch properly by making an initial push
    // This prevents the unborn HEAD issue during concurrent pushes
    {
        let temp_dir = TempDir::new().unwrap();
        let clone_path = temp_dir.path().join("repo");

        clone_repository(&config.get_full_host(), &identifier, &clone_path)
            .await
            .unwrap();

        // Make sure we're on main and push an initial commit to establish the branch
        configure_git_user(&clone_path).await.unwrap();

        // Create an initial commit on main to establish the HEAD properly
        let output = Command::new("git")
            .args(&[
                "commit",
                "--allow-empty",
                "-m",
                "Initial commit to establish main branch",
            ])
            .current_dir(&clone_path)
            .output()
            .await
            .unwrap();

        if !output.status.success() {
            // If commit fails, it might be because there's already a commit, which is fine
            println!(
                "Initial commit may have already existed: {}",
                String::from_utf8_lossy(&output.stderr)
            );
        }

        // Push to establish the main branch on the server
        let output = Command::new("git")
            .args(&["push", "origin", "main"])
            .current_dir(&clone_path)
            .output()
            .await
            .unwrap();

        if !output.status.success() {
            println!(
                "Initial push result: {}",
                String::from_utf8_lossy(&output.stderr)
            );
        }
    }

    // Now create multiple clones for concurrent pushes
    let mut handles = vec![];
    for i in 0..3 {
        let identifier = identifier.clone();
        let host = config.get_full_host();

        let handle = tokio::spawn(async move {
            let temp_dir = TempDir::new().unwrap();
            let clone_path = temp_dir.path().join("repo");

            clone_repository(&host, &identifier, &clone_path)
                .await
                .unwrap();
            create_and_push_branch(
                &clone_path,
                &format!("branch-{}", i),
                &format!("Commit {}", i),
            )
            .await
        });

        handles.push(handle);
    }

    // Wait for all pushes to complete
    for handle in handles {
        handle.await.unwrap().unwrap();
    }

    // Verify all branches were created
    for i in 0..3 {
        verify_branch_exists(&identifier, &format!("branch-{}", i)).await;
    }
}

// ============================================================================
// Helper Functions
// ============================================================================

async fn make_receive_pack_advertisement_request(
    owner: &str,
    repo: &str,
    protocol_version: Option<&str>,
) -> (reqwest::StatusCode, HeaderMap, Vec<u8>) {
    let config = load_config();
    let url = format!(
        "{}/{}/{}.git/info/refs?service=git-receive-pack",
        config.get_full_host(),
        owner,
        repo
    );

    let mut headers = HeaderMap::new();
    if let Some(version) = protocol_version {
        headers.insert("Git-Protocol", HeaderValue::from_str(version).unwrap());
    }

    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await
        .expect("Request should succeed");

    let status = response.status();
    let headers = response.headers().clone();
    let body = response
        .bytes()
        .await
        .expect("Body should be readable")
        .to_vec();

    (status, headers, body)
}

async fn clone_repository(
    host: &str,
    identifier: &RepoIdentifier,
    clone_path: &PathBuf,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let clone_url = format!("{}/{}/{}.git", host, identifier.owner, identifier.repo);

    let output = Command::new("git")
        .args(&["clone", &clone_url, clone_path.to_str().unwrap()])
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git clone failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn create_and_push_branch(
    repo_path: &PathBuf,
    branch_name: &str,
    commit_message: &str,
) -> TestResult {
    // Configure git user identity first
    configure_git_user(repo_path)
        .await
        .map_err(|e| format!("Failed to configure git user: {}", e))?;

    // Checkout or create branch
    let output = Command::new("git")
        .args(&["checkout", "-b", branch_name])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        // Branch might already exist, try to checkout
        Command::new("git")
            .args(&["checkout", branch_name])
            .current_dir(repo_path)
            .output()
            .await?;
    }

    // Create empty commit
    let output = Command::new("git")
        .args(&["commit", "--allow-empty", "-m", commit_message])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git commit failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    // Push branch
    push_branch(repo_path, branch_name).await
}

async fn create_and_push_commit(repo_path: &PathBuf, commit_message: &str) -> TestResult {
    // Configure git user identity first
    configure_git_user(repo_path)
        .await
        .map_err(|e| format!("Failed to configure git user: {}", e))?;

    // Create empty commit
    let output = Command::new("git")
        .args(&["commit", "--allow-empty", "-m", commit_message])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git commit failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    // Push current branch
    let output = Command::new("git")
        .args(&["push"])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git push failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn push_branch(repo_path: &PathBuf, branch_name: &str) -> TestResult {
    let output = Command::new("git")
        .args(&["push", "--set-upstream", "origin", branch_name])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git push failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn force_push_branch(repo_path: &PathBuf, branch_name: &str) -> TestResult {
    let output = Command::new("git")
        .args(&["push", "--force", "origin", branch_name])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git force push failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn delete_remote_branch(repo_path: &PathBuf, branch_name: &str) -> TestResult {
    let output = Command::new("git")
        .args(&["push", "origin", "--delete", branch_name])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git branch delete failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn reset_to_previous_commit(repo_path: &PathBuf) -> TestResult {
    let output = Command::new("git")
        .args(&["reset", "--hard", "HEAD~1"])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git reset failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn create_and_push_tag(repo_path: &PathBuf, tag_name: &str, tag_message: &str) -> TestResult {
    // Configure git user identity first
    configure_git_user(repo_path)
        .await
        .map_err(|e| format!("Failed to configure git user: {}", e))?;

    // Create annotated tag
    let output = Command::new("git")
        .args(&["tag", "-a", tag_name, "-m", tag_message])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git tag failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    // Push tag
    let output = Command::new("git")
        .args(&["push", "origin", tag_name])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git push tag failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

async fn create_test_file(repo_path: &PathBuf, filename: &str, content: &str) -> TestResult {
    use tokio::fs::File;
    use tokio::io::AsyncWriteExt;

    let file_path = repo_path.join(filename);
    let mut file = File::create(file_path).await?;
    file.write_all(content.as_bytes()).await?;
    file.flush().await?;

    Ok(())
}

async fn create_large_file(repo_path: &PathBuf, filename: &str, size_bytes: usize) -> TestResult {
    use tokio::fs::File;
    use tokio::io::AsyncWriteExt;

    let file_path = repo_path.join(filename);
    let mut file = File::create(file_path).await?;

    // Write random data
    let data = vec![b'A'; size_bytes];
    file.write_all(&data).await?;
    file.flush().await?;

    Ok(())
}

async fn add_and_commit(repo_path: &PathBuf, commit_message: &str) -> TestResult {
    // Configure git user identity first
    configure_git_user(repo_path)
        .await
        .map_err(|e| format!("Failed to configure git user: {}", e))?;

    // Add all files
    let output = Command::new("git")
        .args(&["add", "."])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git add failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    // Commit
    let output = Command::new("git")
        .args(&["commit", "-m", commit_message])
        .current_dir(repo_path)
        .output()
        .await?;

    if !output.status.success() {
        return Err(format!(
            "Git commit failed: {}",
            String::from_utf8_lossy(&output.stderr)
        )
        .into());
    }

    Ok(())
}

// ============================================================================
// Validation and Verification Functions
// ============================================================================

fn validate_v1_receive_pack_advertisement(pkt_lines: &[Vec<u8>]) {
    assert!(!pkt_lines.is_empty(), "Advertisement should not be empty");

    // First line should be service announcement
    let service_line = String::from_utf8_lossy(&pkt_lines[0]);
    assert!(service_line.contains("# service=git-receive-pack"));

    // Second line should be flush
    assert!(
        pkt_lines[1].is_empty(),
        "Second line should be flush packet"
    );

    // Should have capabilities line (either with refs or standalone)
    let mut found_capabilities = false;
    for line in pkt_lines.iter().skip(2) {
        if line.is_empty() {
            break; // End of refs
        }
        let line_str = String::from_utf8_lossy(line);
        if line_str.contains('\0') {
            // This line has capabilities
            found_capabilities = true;
            assert!(
                line_str.contains("report-status"),
                "Should have report-status capability"
            );
        }
    }

    // For receive-pack, we should always have capabilities
    assert!(
        found_capabilities,
        "Should have capabilities in receive-pack advertisement"
    );
}

fn validate_v2_receive_pack_advertisement(pkt_lines: &[Vec<u8>]) {
    assert!(!pkt_lines.is_empty(), "Advertisement should not be empty");

    // First line should be service announcement
    let service_line = String::from_utf8_lossy(&pkt_lines[0]);
    assert!(service_line.contains("# service=git-receive-pack"));

    // Second line should be flush
    assert!(
        pkt_lines[1].is_empty(),
        "Second line should be flush packet"
    );

    // Third line should be version 2
    let version_line = String::from_utf8_lossy(&pkt_lines[2]);
    assert!(version_line.contains("version 2"));

    // Should have capabilities
    let mut found_capabilities = false;
    for line in pkt_lines.iter().skip(3) {
        if line.is_empty() {
            break; // End of capabilities
        }
        let line_str = String::from_utf8_lossy(line);
        if line_str.contains("report-status") || line_str.contains("push-options") {
            found_capabilities = true;
        }
    }

    assert!(
        found_capabilities,
        "Should have receive-pack capabilities in v2 advertisement"
    );
}

async fn verify_branch_exists(identifier: &RepoIdentifier, branch_name: &str) {
    let (status, _headers, body) = make_receive_pack_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        None, // v1
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    let mut found_branch = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }
        let line_str = String::from_utf8_lossy(line);
        if line_str.contains(&format!("refs/heads/{}", branch_name)) {
            found_branch = true;
            break;
        }
    }

    assert!(
        found_branch,
        "Branch '{}' should exist on server",
        branch_name
    );
}

async fn verify_branch_not_exists(identifier: &RepoIdentifier, branch_name: &str) {
    let (status, _headers, body) = make_receive_pack_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        None, // v1
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }
        let line_str = String::from_utf8_lossy(line);
        assert!(
            !line_str.contains(&format!("refs/heads/{}", branch_name)),
            "Branch '{}' should not exist on server",
            branch_name
        );
    }
}

async fn verify_tag_exists(identifier: &RepoIdentifier, tag_name: &str) {
    let (status, _headers, body) = make_receive_pack_advertisement_request(
        &identifier.owner,
        &identifier.repo,
        None, // v1
    )
    .await;

    assert_eq!(status, reqwest::StatusCode::OK);
    let pkt_lines = parse_pkt_lines(&body);

    let mut found_tag = false;
    for line in &pkt_lines {
        if line.is_empty() {
            continue;
        }
        let line_str = String::from_utf8_lossy(line);
        if line_str.contains(&format!("refs/tags/{}", tag_name)) {
            found_tag = true;
            break;
        }
    }

    assert!(found_tag, "Tag '{}' should exist on server", tag_name);
}

async fn verify_commit_count(
    identifier: &RepoIdentifier,
    branch_name: &str,
    expected_count: usize,
) {
    // This is a simplified verification - in a real implementation, you'd need to
    // fetch the actual commit history. For now, we'll just verify the branch exists.
    verify_branch_exists(identifier, branch_name).await;

    // TODO: Implement actual commit count verification by fetching commit history
    // This would require implementing a git log equivalent or using the upload-pack protocol
    println!(
        "Note: Commit count verification for {} commits on {} is simplified",
        expected_count, branch_name
    );
}

async fn verify_file_exists(identifier: &RepoIdentifier, branch_name: &str, filename: &str) {
    // This is a simplified verification - in a real implementation, you'd need to
    // fetch the actual file content. For now, we'll just verify the branch exists.
    verify_branch_exists(identifier, branch_name).await;

    // TODO: Implement actual file existence verification by fetching tree objects
    // This would require implementing tree traversal or using the upload-pack protocol
    println!(
        "Note: File existence verification for {} on {} is simplified",
        filename, branch_name
    );
}
