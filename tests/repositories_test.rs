use serde_json::{Value, json};

use axum_git_server::config::load_config;

mod common;

#[tokio::test]
async fn test_create_repository() {
    let config = load_config();
    let client = reqwest::Client::new();

    let repo_name = format!("test-repo-{}", uuid::Uuid::new_v4());

    let response = client
        .post(format!("{}/repositories", config.get_full_host()))
        .json(&json!({
            "owner": "test-owner",
            "repo": repo_name,
            "default_branch": "main",
            "description": "Test repository"
        }))
        .send()
        .await
        .unwrap();

    assert_eq!(response.status(), reqwest::StatusCode::CREATED);

    let body: Value = response.json().await.unwrap();

    assert_eq!(body["owner"], "test-owner");
    assert_eq!(body["repo"], repo_name);
    assert_eq!(body["default_branch"], "main");
    assert_eq!(
        body["clone_url"],
        format!("{}/test-owner/{}.git", config.get_full_host(), repo_name)
    );

    // TODO: These don't work when running the tests through docker
    // Verify repository was actually created
    // let repo_path = PathBuf::from(format!("./repos/test-owner/${}.git", repo_name));
    // assert!(repo_path.exists());
    // assert!(repo_path.join("HEAD").exists());
    // assert!(repo_path.join("description").exists());

    // Test duplicate repository creation
    let second_response = client
        .post(format!("{}/repositories", config.get_full_host()))
        .json(&json!({
            "owner": "test-owner",
            "repo": repo_name,
            "default_branch": "main",
            "description": "Test repository"
        }))
        .send()
        .await
        .unwrap();
    assert_eq!(second_response.status(), reqwest::StatusCode::BAD_REQUEST);

    let second_body: Value = second_response.json().await.unwrap();
    assert_eq!(second_body["message"], "Repository already exists");
}
