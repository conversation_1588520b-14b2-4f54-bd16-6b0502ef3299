use axum_git_server::types::git::filter::{GitFilter, parse_fetch_request};

#[cfg(test)]
mod common;

use common::test_helpers::{
    create_random_repo_identifier, create_test_repo_through_api,
};

#[tokio::test]
async fn test_git_filter_parsing() {
    // Test blob:none filter
    let filter = GitFilter::parse("blob:none").unwrap();
    assert_eq!(filter, GitFilter::BlobNone);
    assert!(filter.excludes_blobs());
    assert!(!filter.excludes_trees());

    // Test blob:limit filter
    let filter = GitFilter::parse("blob:limit=1024").unwrap();
    assert_eq!(filter, GitFilter::BlobLimit(1024));
    assert!(!filter.excludes_blobs());
    assert_eq!(filter.max_blob_size(), Some(1024));

    // Test tree filter
    let filter = GitFilter::parse("tree:0").unwrap();
    assert_eq!(filter, GitFilter::TreeDepth(0));
    assert!(filter.excludes_trees());

    // Test combined filter
    let filter = GitFilter::parse("combine:blob:none+tree:1").unwrap();
    if let GitFilter::Combine(filters) = filter {
        assert_eq!(filters.len(), 2);
        assert_eq!(filters[0], GitFilter::BlobNone);
        assert_eq!(filters[1], GitFilter::TreeDepth(1));
    } else {
        panic!("Expected Combine filter");
    }
}

#[tokio::test]
async fn test_fetch_request_parsing() {
    let request_body = b"command=fetch\nwant abc123def456\nhave 789xyz\nfilter blob:none\nshallow 5\ndone\n";
    let request = parse_fetch_request(request_body).unwrap();

    assert_eq!(request.wants, vec!["abc123def456"]);
    assert_eq!(request.haves, vec!["789xyz"]);
    assert_eq!(request.filter, Some(GitFilter::BlobNone));
    assert_eq!(request.shallow.depth, Some(5));
    assert!(request.done);
}

#[tokio::test]
async fn test_fetch_request_with_multiple_wants_and_haves() {
    let request_body = b"command=fetch\nwant abc123\nwant def456\nhave 111222\nhave 333444\nfilter blob:limit=1048576\ndone\n";
    let request = parse_fetch_request(request_body).unwrap();

    assert_eq!(request.wants, vec!["abc123", "def456"]);
    assert_eq!(request.haves, vec!["111222", "333444"]);
    assert_eq!(request.filter, Some(GitFilter::BlobLimit(1048576)));
    assert!(request.done);
}

#[tokio::test]
async fn test_fetch_request_with_combined_filter() {
    let request_body = b"command=fetch\nwant abc123\nfilter combine:blob:none+tree:1\ndone\n";
    let request = parse_fetch_request(request_body).unwrap();

    assert_eq!(request.wants, vec!["abc123"]);
    if let Some(GitFilter::Combine(filters)) = request.filter {
        assert_eq!(filters.len(), 2);
        assert_eq!(filters[0], GitFilter::BlobNone);
        assert_eq!(filters[1], GitFilter::TreeDepth(1));
    } else {
        panic!("Expected Combine filter");
    }
}

#[tokio::test]
async fn test_fetch_request_with_shallow_options() {
    let request_body = b"command=fetch\nwant abc123\nshallow 10\ndeepen 5\ndeepen-since 2023-01-01\ndeepen-not refs/heads/main\ndone\n";
    let request = parse_fetch_request(request_body).unwrap();

    assert_eq!(request.wants, vec!["abc123"]);
    assert_eq!(request.shallow.depth, Some(10));
    assert_eq!(request.shallow.deepen, Some(5));
    assert_eq!(request.shallow.deepen_since, Some("2023-01-01".to_string()));
    assert_eq!(request.shallow.deepen_not, vec!["refs/heads/main"]);
    assert!(request.done);
}

#[tokio::test]
async fn test_filter_display() {
    assert_eq!(GitFilter::BlobNone.to_string(), "blob:none");
    assert_eq!(GitFilter::BlobLimit(1024).to_string(), "blob:limit=1024");
    assert_eq!(GitFilter::TreeDepth(0).to_string(), "tree:0");
    
    let combined = GitFilter::Combine(vec![
        GitFilter::BlobNone,
        GitFilter::TreeDepth(1)
    ]);
    assert_eq!(combined.to_string(), "combine:blob:none+tree:1");
}

#[tokio::test]
async fn test_filter_properties() {
    // Test blob exclusion
    assert!(GitFilter::BlobNone.excludes_blobs());
    assert!(!GitFilter::BlobLimit(1024).excludes_blobs());
    assert!(GitFilter::TreeDepth(0).excludes_blobs());

    // Test tree exclusion
    assert!(!GitFilter::BlobNone.excludes_trees());
    assert!(!GitFilter::BlobLimit(1024).excludes_trees());
    assert!(GitFilter::TreeDepth(0).excludes_trees());

    // Test max blob size
    assert_eq!(GitFilter::BlobNone.max_blob_size(), None);
    assert_eq!(GitFilter::BlobLimit(1024).max_blob_size(), Some(1024));
    assert_eq!(GitFilter::TreeDepth(0).max_blob_size(), None);

    // Test combined filter properties
    let combined = GitFilter::Combine(vec![
        GitFilter::BlobNone,
        GitFilter::BlobLimit(512)
    ]);
    assert!(combined.excludes_blobs()); // BlobNone takes precedence
    assert_eq!(combined.max_blob_size(), Some(512)); // Minimum size
}

#[tokio::test]
async fn test_invalid_filter_parsing() {
    assert!(GitFilter::parse("invalid:filter").is_err());
    assert!(GitFilter::parse("blob:invalid").is_err());
    assert!(GitFilter::parse("tree:invalid").is_err());
    assert!(GitFilter::parse("blob:limit=invalid").is_err());
}

#[tokio::test]
async fn test_empty_fetch_request() {
    let request_body = b"command=fetch\ndone\n";
    let request = parse_fetch_request(request_body).unwrap();

    assert!(request.wants.is_empty());
    assert!(request.haves.is_empty());
    assert!(request.filter.is_none());
    assert!(request.done);
}

// Integration test with actual repository
#[tokio::test]
async fn test_filtered_fetch_integration() {
    let repo_id = create_random_repo_identifier();

    // Create a test repository
    create_test_repo_through_api(&repo_id, "main").await;

    // Test that the server can handle filter requests
    // Note: This test verifies the parsing and structure, not the actual git operations
    // which would require a more complex setup with actual git operations

    let request_body = format!(
        "command=fetch\nwant {}\nfilter blob:none\ndone\n",
        "0000000000000000000000000000000000000000" // Placeholder SHA
    );

    let request = parse_fetch_request(request_body.as_bytes()).unwrap();
    assert_eq!(request.filter, Some(GitFilter::BlobNone));

    println!("✅ Filter parsing works correctly for integration scenario");
}

#[tokio::test]
async fn test_sparse_filter_parsing() {
    let filter = GitFilter::parse("sparse:src/,docs/,README.md").unwrap();
    if let GitFilter::Sparse(paths) = filter {
        assert_eq!(paths, vec!["src/", "docs/", "README.md"]);
    } else {
        panic!("Expected Sparse filter");
    }
}

#[tokio::test]
async fn test_fetch_request_with_capabilities() {
    let request_body = b"command=fetch\nwant abc123\nthin-pack\nside-band-64k\nofs-delta\ndone\n";
    let request = parse_fetch_request(request_body).unwrap();

    assert_eq!(request.wants, vec!["abc123"]);
    assert!(request.capabilities.contains(&"thin-pack".to_string()));
    assert!(request.capabilities.contains(&"side-band-64k".to_string()));
    assert!(request.capabilities.contains(&"ofs-delta".to_string()));
    assert!(request.done);
}
