use std::process::Command;
use tokio::fs;

use axum_git_server::{config::load_config, error::AppError, helpers::temp::TempDir};

#[cfg(test)]
mod common;

use common::test_helpers::{
    clone_repo_to_temp, configure_git_user, create_branch_with_empty_commit_and_push,
    create_random_repo_identifier, create_test_repo_through_api,
};

// ============================================================================
// COMPREHENSIVE GIT BINARY CLIENT TEST SUITE
// ============================================================================

#[tokio::test]
async fn test_git_binary_clone_empty_repository() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create empty repository
    create_test_repo_through_api(&repo_identifier, "main").await;

    let temp_dir = TempDir::new("git-binary-clone-empty")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Attempt to clone empty repository
    let output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(output.status.success());
    assert!(clone_path.exists());
    let status_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "status", "--porcelain"])
        .output()
        .expect("Failed to execute git status");

    assert!(status_output.status.success());
    println!("Successfully cloned empty repository");

    Ok(())
}

#[tokio::test]
async fn test_git_binary_clone_with_commits() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository and add commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("git-binary-clone-commits")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Clone repository with commits
    let output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(
        output.status.success(),
        "Git clone should succeed: {}",
        String::from_utf8_lossy(&output.stderr)
    );
    assert!(clone_path.exists());

    // Verify repository state
    let log_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "log", "--oneline"])
        .output()
        .expect("Failed to execute git log");

    assert!(log_output.status.success());
    let log_content = String::from_utf8_lossy(&log_output.stdout);
    assert!(log_content.contains("Initial commit"));

    println!("Successfully cloned repository with commits");
    Ok(())
}

#[tokio::test]
async fn test_git_binary_fetch_multiple_branches() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with multiple branches
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;

    // Create main branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Main commit").await?;

    // Create feature branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "feature", "Feature commit").await?;

    // Create development branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "development", "Dev commit").await?;

    // Clone repository
    let temp_dir = TempDir::new("git-binary-multi-branch")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(clone_output.status.success());

    // Fetch all branches
    let fetch_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "fetch", "origin"])
        .output()
        .expect("Failed to execute git fetch");

    assert!(fetch_output.status.success());

    // List remote branches
    let branch_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "branch", "-r"])
        .output()
        .expect("Failed to execute git branch");

    assert!(branch_output.status.success());
    let branches = String::from_utf8_lossy(&branch_output.stdout);

    assert!(branches.contains("origin/main"));
    assert!(branches.contains("origin/feature"));
    assert!(branches.contains("origin/development"));

    println!(
        "Successfully fetched multiple branches: {}",
        branches.trim()
    );
    Ok(())
}

#[tokio::test]
async fn test_git_binary_push_new_branch() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with initial commit
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    // Clone repository for testing
    let temp_dir = TempDir::new("git-binary-push-branch")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(clone_output.status.success());

    // Configure git user
    configure_git_user(&clone_path).await?;

    // Create new branch
    let checkout_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "checkout",
            "-b",
            "new-feature",
        ])
        .output()
        .expect("Failed to execute git checkout");

    assert!(checkout_output.status.success());

    // Create a file and commit
    let test_file = clone_path.join("test.txt");
    fs::write(&test_file, "test content")
        .await
        .expect("Failed to write test file");

    let add_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "add", "test.txt"])
        .output()
        .expect("Failed to execute git add");

    assert!(add_output.status.success());

    let commit_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "commit",
            "-m",
            "New feature commit",
        ])
        .output()
        .expect("Failed to execute git commit");

    assert!(commit_output.status.success());

    // Push new branch
    let push_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "push",
            "origin",
            "new-feature",
        ])
        .output()
        .expect("Failed to execute git push");

    assert!(
        push_output.status.success(),
        "Git push should succeed: {}",
        String::from_utf8_lossy(&push_output.stderr)
    );

    println!("Successfully pushed new branch");
    Ok(())
}

#[tokio::test]
async fn test_git_binary_list_remote_references() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with multiple branches
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;

    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Main commit").await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "feature", "Feature commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // List remote references
    let ls_remote_output = Command::new("git")
        .args(&["ls-remote", &remote_url])
        .output()
        .expect("Failed to execute git ls-remote");

    assert!(ls_remote_output.status.success());
    let refs = String::from_utf8_lossy(&ls_remote_output.stdout);

    // Verify expected references exist
    assert!(refs.contains("refs/heads/main"));
    assert!(refs.contains("refs/heads/feature"));
    assert!(refs.contains("HEAD"));

    println!("Remote references: {}", refs.trim());
    Ok(())
}

#[tokio::test]
async fn test_git_binary_error_handling_nonexistent_repo() -> Result<(), AppError> {
    let config = load_config();

    let remote_url = format!("{}/nonexistent/repo.git", config.get_full_host());

    // Attempt to clone non-existent repository
    let output = Command::new("git")
        .args(&["clone", &remote_url, "/tmp/nonexistent-clone"])
        .output()
        .expect("Failed to execute git clone");

    // Git binary should handle this gracefully
    if output.status.success() {
        println!("Unexpected success cloning non-existent repo");
        // Clean up if it somehow succeeded
        let _ = std::fs::remove_dir_all("/tmp/nonexistent-clone");
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("Expected error for non-existent repo: {}", stderr);
    }

    Ok(())
}

#[tokio::test]
async fn test_git_binary_fetch_specific_commit() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    let commit_oid =
        create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Target commit").await?;

    // Clone repository
    let temp_dir = TempDir::new("git-binary-fetch-commit")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(clone_output.status.success());

    // Fetch specific commit (this will fetch the branch containing it)
    let fetch_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "fetch",
            "origin",
            "main",
        ])
        .output()
        .expect("Failed to execute git fetch");

    assert!(fetch_output.status.success());

    // Verify the commit exists
    let show_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "show",
            &commit_oid,
            "--format=%H",
        ])
        .output()
        .expect("Failed to execute git show");

    assert!(show_output.status.success());
    let shown_commit = String::from_utf8_lossy(&show_output.stdout);
    assert!(shown_commit.contains(&commit_oid));

    println!("Successfully fetched specific commit: {}", commit_oid);
    Ok(())
}

#[tokio::test]
async fn test_git_binary_concurrent_operations() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Spawn multiple concurrent clone operations
    let mut handles = Vec::new();

    for i in 0..3 {
        let url = remote_url.clone();
        let handle = tokio::spawn(async move {
            let temp_dir = TempDir::new(&format!("git-binary-concurrent-{}", i))
                .await
                .expect("temp dir created");
            let clone_path = temp_dir.path.join("cloned-repo");

            let output = Command::new("git")
                .args(&["clone", &url, clone_path.to_str().unwrap()])
                .output()
                .expect("Failed to execute git clone");

            assert!(
                output.status.success(),
                "Concurrent clone {} should succeed: {}",
                i,
                String::from_utf8_lossy(&output.stderr)
            );

            assert!(clone_path.exists());

            println!("Concurrent clone {} completed successfully", i);
        });
        handles.push(handle);
    }

    // Wait for all operations to complete
    for handle in handles {
        handle.await.expect("Concurrent operation should complete");
    }

    println!("All concurrent operations completed successfully");
    Ok(())
}

#[tokio::test]
async fn test_git_binary_repository_info() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("git-binary-repo-info")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(clone_output.status.success());

    // Test repository information
    assert!(clone_path.exists());
    assert!(clone_path.join(".git").exists());

    // Test HEAD information
    let branch_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "branch",
            "--show-current",
        ])
        .output()
        .expect("Failed to execute git branch");

    assert!(branch_output.status.success());
    let current_branch = String::from_utf8_lossy(&branch_output.stdout);
    assert_eq!(current_branch.trim(), "main");

    // Test remote information
    let remote_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "remote", "-v"])
        .output()
        .expect("Failed to execute git remote");

    assert!(remote_output.status.success());
    let remotes = String::from_utf8_lossy(&remote_output.stdout);
    assert!(remotes.contains("origin"));
    assert!(remotes.contains(&remote_url));

    // Test status
    let status_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "status", "--porcelain"])
        .output()
        .expect("Failed to execute git status");

    assert!(status_output.status.success());
    let status = String::from_utf8_lossy(&status_output.stdout);
    assert!(
        status.trim().is_empty(),
        "Working directory should be clean"
    );

    println!("Repository information verified successfully");
    Ok(())
}

#[tokio::test]
async fn test_git_binary_tag_operations() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    // Clone repository for testing
    let temp_dir = TempDir::new("git-binary-tags")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("git")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute git clone");

    assert!(clone_output.status.success());

    // Configure git user
    configure_git_user(&clone_path).await?;

    // Create a tag
    let tag_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "tag",
            "-a",
            "v1.0.0",
            "-m",
            "Version 1.0.0",
        ])
        .output()
        .expect("Failed to execute git tag");

    assert!(tag_output.status.success());

    // Push tags
    let push_tags_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "push",
            "origin",
            "--tags",
        ])
        .output()
        .expect("Failed to execute git push --tags");

    assert!(
        push_tags_output.status.success(),
        "Git push tags should succeed: {}",
        String::from_utf8_lossy(&push_tags_output.stderr)
    );

    // List remote tags
    let ls_remote_tags_output = Command::new("git")
        .args(&["ls-remote", "--tags", &remote_url])
        .output()
        .expect("Failed to execute git ls-remote --tags");

    assert!(ls_remote_tags_output.status.success());
    let tags = String::from_utf8_lossy(&ls_remote_tags_output.stdout);
    assert!(tags.contains("refs/tags/v1.0.0"));

    println!("Tag operations completed successfully: {}", tags.trim());
    Ok(())
}

#[tokio::test]
async fn test_git_binary_protocol_versions() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Test with protocol version 1
    let temp_dir_v1 = TempDir::new("git-binary-protocol-v1")
        .await
        .expect("temp dir created");
    let clone_path_v1 = temp_dir_v1.path.join("cloned-repo");

    let clone_v1_output = Command::new("git")
        .args(&[
            "-c",
            "protocol.version=1",
            "clone",
            &remote_url,
            clone_path_v1.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone with protocol v1");

    assert!(
        clone_v1_output.status.success(),
        "Clone with protocol v1 should succeed: {}",
        String::from_utf8_lossy(&clone_v1_output.stderr)
    );

    // Test with protocol version 2
    let temp_dir_v2 = TempDir::new("git-binary-protocol-v2")
        .await
        .expect("temp dir created");
    let clone_path_v2 = temp_dir_v2.path.join("cloned-repo");

    let clone_v2_output = Command::new("git")
        .args(&[
            "-c",
            "protocol.version=2",
            "clone",
            &remote_url,
            clone_path_v2.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute git clone with protocol v2");

    assert!(
        clone_v2_output.status.success(),
        "Clone with protocol v2 should succeed: {}",
        String::from_utf8_lossy(&clone_v2_output.stderr)
    );

    println!("Both protocol versions work successfully");
    Ok(())
}
