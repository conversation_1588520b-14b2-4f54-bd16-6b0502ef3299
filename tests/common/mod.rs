#[cfg(test)]
#[allow(dead_code)]
pub mod test_helpers {
    use rand::Rng;
    use reqwest::header::{HeaderMap, HeaderValue};
    use sha1::{Digest, Sha1};
    use std::path::PathBuf;
    use uuid::Uuid;

    use axum_git_server::{
        config::AppConfig, error::AppError, helpers::temp, internal::command::run_command,
        types::repository::RepoIdentifier,
    };
    use serde_json::{Value, json};

    pub fn not_our_ref_res_body(commit: &str) -> String {
        format!("0049ERR upload-pack: not our ref {}", commit)
    }

    pub fn create_want_request_url(config: AppConfig, repo: RepoIdentifier) -> String {
        format!(
            "{}/{}/{}.git/git-upload-pack",
            config.get_full_host(),
            repo.owner,
            repo.repo
        )
    }

    pub fn create_want_request_headers(version: &str) -> HeaderMap {
        let mut headers = HeaderMap::new();
        // HeaderName::
        headers.insert(
            "Content-Type",
            HeaderValue::from_static("application/x-git-upload-pack-request"),
        );
        headers.insert("User-Agent", HeaderValue::from_static("git/2.47.0"));
        headers.insert(
            "Git-Protocol",
            HeaderValue::from_str(version).expect("git protocol version set"),
        );

        headers
    }

    pub fn generate_random_sha1() -> String {
        let mut rng = rand::rng();
        let random_data: [u8; 32] = rng.random();

        let mut hasher = Sha1::new();
        hasher.update(&random_data);
        let result = hasher.finalize();

        hex::encode(result)
    }

    pub fn create_random_repo_identifier() -> RepoIdentifier {
        RepoIdentifier {
            owner: format!("owner-{}", Uuid::new_v4().to_string()),
            repo: format!("repo-{}", Uuid::new_v4().to_string()),
        }
    }

    pub async fn create_test_repo_through_api(repo_identifier: &RepoIdentifier, branch: &str) {
        let config = axum_git_server::config::load_config();
        let client = reqwest::Client::new();

        let url = format!("{}/repositories", config.get_full_host());
        println!("Creating test repository through API: {}", &url);
        let response = client
            .post(url)
            .json(&json!({
                "owner": &repo_identifier.owner,
                "repo": repo_identifier.repo,
                "default_branch": branch,
            }))
            .send()
            .await
            .unwrap();

        assert_eq!(response.status(), reqwest::StatusCode::CREATED);

        let body: Value = response.json().await.unwrap();

        println!("create response body: {:?}", body);
    }

    pub async fn clone_repo_to_temp(
        base_url: &str,
        repo_identifier: &RepoIdentifier,
    ) -> Result<PathBuf, AppError> {
        let temp_dir = temp::TempDir::new("clone").await.expect("temp dir created");

        println!("Created temp dir: {:?}", &temp_dir.path);

        let clone_url = format!(
            "{}/{}/{}.git",
            base_url, repo_identifier.owner, repo_identifier.repo
        );

        println!("Client clone URL: {}", clone_url);

        run_command("git", &["clone", &clone_url], Some(&temp_dir.path), None).await?;

        let clone_path = temp_dir.path.join(&repo_identifier.repo);
        println!("Cloned repository to {:?}", clone_path);

        // Configure git user identity
        configure_git_user(&clone_path).await?;

        Ok(clone_path)
    }

    pub async fn create_branch_with_empty_commit_and_push(
        repo_path: &PathBuf,
        branch: &str,
        commit_message: &str,
    ) -> Result<String, AppError> {
        println!("Create branch and empty commit in repo: {:?}", repo_path);
        println!("Creating branch {} with empty commit and pushing", branch);

        // Configure git user identity first
        configure_git_user(repo_path).await?;

        run_command("git", &["checkout", "-b", branch], Some(&repo_path), None).await?;
        println!("Branch created successfully");

        run_command(
            "git",
            &["commit", "--allow-empty", "-m", commit_message],
            Some(&repo_path),
            None,
        )
        .await?;
        println!("Empty commit created successfully");

        run_command(
            "git",
            &["push", "--set-upstream", "origin", &branch],
            Some(&repo_path),
            None,
        )
        .await?;
        println!("Pushed branch successfully");

        let rev_parse_stdout =
            run_command("git", &["rev-parse", "HEAD"], Some(&repo_path), None).await?;
        let rev_parse_result = String::from_utf8_lossy(&rev_parse_stdout).to_string();

        println!("rev-parse stdout: {}", rev_parse_result);
        Ok(rev_parse_result.trim().to_string())
    }

    /// Configure git user identity for testing
    pub async fn configure_git_user(repo_path: &PathBuf) -> Result<(), AppError> {
        // Set user name
        run_command(
            "git",
            &["config", "user.name", "Test User"],
            Some(repo_path),
            None,
        )
        .await?;

        // Set user email
        run_command(
            "git",
            &["config", "user.email", "<EMAIL>"],
            Some(repo_path),
            None,
        )
        .await?;

        Ok(())
    }

    pub fn parse_pkt_lines(mut bytes: &[u8]) -> Vec<Vec<u8>> {
        let mut lines = Vec::new();

        while !bytes.is_empty() {
            if bytes.len() < 4 {
                panic!("invalid pkt-line: too short for length prefix");
            }

            let len_str = std::str::from_utf8(&bytes[0..4]).expect("valid UTF-8 hex length");
            let len = usize::from_str_radix(len_str, 16).expect("valid hex length");

            if len == 0 {
                // Flush pkt-line
                lines.push(Vec::new());
                bytes = &bytes[4..];
                continue;
            }

            if len < 4 || len > bytes.len() {
                panic!("invalid pkt-line: length out of range");
            }

            lines.push(bytes[4..len].to_vec());
            bytes = &bytes[len..];
        }

        lines
    }
}
