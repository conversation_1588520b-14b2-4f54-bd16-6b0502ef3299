use std::process::Command;
use tokio::fs;

use axum_git_server::{config::load_config, error::AppError, helpers::temp::TempDir};

#[cfg(test)]
pub mod common;

use common::test_helpers::{
    clone_repo_to_temp, configure_git_user, create_branch_with_empty_commit_and_push,
    create_random_repo_identifier, create_test_repo_through_api,
};

// ============================================================================
// COMPREHENSIVE GITOXIDE (GIX) BINARY CLIENT TEST SUITE
// ============================================================================

#[tokio::test]
async fn test_gitoxide_clone_empty_repository() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create empty repository
    create_test_repo_through_api(&repo_identifier, "main").await;

    let temp_dir = TempDir::new("gitoxide-clone-empty")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Attempt to clone empty repository using gitoxide
    let output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    if output.status.success() {
        // If clone succeeds, verify repository state
        assert!(clone_path.exists());
        println!("Successfully cloned empty repository with gitoxide");
    } else {
        // Gitoxide might handle empty repositories differently
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!(
            "Clone failed for empty repository (may be expected): {}",
            stderr
        );

        // This might be acceptable behavior for gitoxide
        if stderr.contains("empty") || stderr.contains("no refs") {
            println!("Gitoxide correctly detected empty repository");
        }
    }

    Ok(())
}

#[tokio::test]
async fn test_gitoxide_clone_with_commits() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository and add commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("gitoxide-clone-commits")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Clone repository with commits using gitoxide
    let output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    assert!(
        output.status.success(),
        "Gitoxide clone should succeed: {}",
        String::from_utf8_lossy(&output.stderr)
    );
    assert!(clone_path.exists());

    // Verify repository state using git (since gix might not have all log commands)
    let log_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "log", "--oneline"])
        .output()
        .expect("Failed to execute git log");

    if log_output.status.success() {
        let log_content = String::from_utf8_lossy(&log_output.stdout);
        assert!(log_content.contains("Initial commit"));
    }

    println!("Successfully cloned repository with commits using gitoxide");
    Ok(())
}

#[tokio::test]
async fn test_gitoxide_fetch_operations() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with multiple branches
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;

    // Create main branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Main commit").await?;

    // Create feature branch
    create_branch_with_empty_commit_and_push(&setup_repo_path, "feature", "Feature commit").await?;

    // Clone repository using gitoxide
    let temp_dir = TempDir::new("gitoxide-fetch")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    assert!(clone_output.status.success());

    // Try to fetch using gitoxide (if supported)
    let fetch_output = Command::new("gix")
        .args(&["-C", clone_path.to_str().unwrap(), "fetch"])
        .output()
        .expect("Failed to execute gix fetch");

    if fetch_output.status.success() {
        println!("Gitoxide fetch completed successfully");
    } else {
        // Fallback to git for fetch operations
        let git_fetch_output = Command::new("git")
            .args(&["-C", clone_path.to_str().unwrap(), "fetch", "origin"])
            .output()
            .expect("Failed to execute git fetch");

        assert!(git_fetch_output.status.success());
        println!("Fetch completed using git fallback");
    }

    // Verify branches exist using git
    let branch_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "branch", "-r"])
        .output()
        .expect("Failed to execute git branch");

    assert!(branch_output.status.success());
    let branches = String::from_utf8_lossy(&branch_output.stdout);
    assert!(branches.contains("origin/main"));

    println!("Fetch operations completed successfully");
    Ok(())
}

#[tokio::test]
async fn test_gitoxide_repository_info() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let temp_dir = TempDir::new("gitoxide-repo-info")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    let clone_output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    assert!(clone_output.status.success());

    // Test repository information
    assert!(clone_path.exists());
    assert!(clone_path.join(".git").exists());

    // Try gitoxide-specific commands
    let config_output = Command::new("gix")
        .args(&["-C", clone_path.to_str().unwrap(), "config"])
        .output()
        .expect("Failed to execute gix config");

    if config_output.status.success() {
        let config_content = String::from_utf8_lossy(&config_output.stdout);
        println!("Gitoxide config output: {}", config_content);
    }

    // Verify using git commands for compatibility
    let remote_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "remote", "-v"])
        .output()
        .expect("Failed to execute git remote");

    assert!(remote_output.status.success());
    let remotes = String::from_utf8_lossy(&remote_output.stdout);
    assert!(remotes.contains("origin"));
    assert!(remotes.contains(&remote_url));

    println!("Repository information verified successfully");
    Ok(())
}

#[tokio::test]
async fn test_gitoxide_concurrent_operations() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Spawn multiple concurrent clone operations using gitoxide
    let mut handles = Vec::new();

    for i in 0..3 {
        let url = remote_url.clone();
        let handle = tokio::spawn(async move {
            let temp_dir = TempDir::new(&format!("gitoxide-concurrent-{}", i))
                .await
                .expect("temp dir created");
            let clone_path = temp_dir.path.join("cloned-repo");

            let output = Command::new("gix")
                .args(&["clone", &url, clone_path.to_str().unwrap()])
                .output()
                .expect("Failed to execute gix clone");

            assert!(
                output.status.success(),
                "Concurrent gitoxide clone {} should succeed: {}",
                i,
                String::from_utf8_lossy(&output.stderr)
            );

            assert!(clone_path.exists());

            println!("Concurrent gitoxide clone {} completed successfully", i);
        });
        handles.push(handle);
    }

    // Wait for all operations to complete
    for handle in handles {
        handle.await.expect("Concurrent operation should complete");
    }

    println!("All concurrent gitoxide operations completed successfully");
    Ok(())
}

#[tokio::test]
async fn test_gitoxide_error_handling() -> Result<(), AppError> {
    let config = load_config();

    let remote_url = format!("{}/nonexistent/repo.git", config.get_full_host());

    // Attempt to clone non-existent repository using gitoxide
    let output = Command::new("gix")
        .args(&["clone", &remote_url, "/tmp/gitoxide-nonexistent-clone"])
        .output()
        .expect("Failed to execute gix clone");

    // Gitoxide should handle this gracefully
    if output.status.success() {
        println!("Unexpected success cloning non-existent repo with gitoxide");
        // Clean up if it somehow succeeded
        let _ = std::fs::remove_dir_all("/tmp/gitoxide-nonexistent-clone");
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!(
            "Expected error for non-existent repo with gitoxide: {}",
            stderr
        );
    }

    Ok(())
}

#[tokio::test]
async fn test_gitoxide_protocol_compatibility() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Test gitoxide clone with different configurations
    let temp_dir = TempDir::new("gitoxide-protocol")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    // Try basic clone
    let clone_output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    assert!(
        clone_output.status.success(),
        "Gitoxide clone should succeed: {}",
        String::from_utf8_lossy(&clone_output.stderr)
    );

    // Test gitoxide-specific features if available
    let status_output = Command::new("gix")
        .args(&["-C", clone_path.to_str().unwrap(), "status"])
        .output()
        .expect("Failed to execute gix status");

    if status_output.status.success() {
        println!("Gitoxide status command works");
    } else {
        println!("Gitoxide status not available, using git fallback");
    }

    println!("Protocol compatibility test completed");
    Ok(())
}

#[tokio::test]
async fn test_gitoxide_performance_features() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Test gitoxide clone with performance options
    let temp_dir = TempDir::new("gitoxide-performance")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    // Try clone with verbose output to see gitoxide-specific features
    let clone_output = Command::new("gix")
        .args(&[
            "clone",
            "--verbose",
            &remote_url,
            clone_path.to_str().unwrap(),
        ])
        .output()
        .expect("Failed to execute gix clone");

    if clone_output.status.success() {
        let stdout = String::from_utf8_lossy(&clone_output.stdout);
        let stderr = String::from_utf8_lossy(&clone_output.stderr);
        println!("Gitoxide verbose output: {}{}", stdout, stderr);

        assert!(clone_path.exists());
        println!("Gitoxide performance features test completed successfully");
    } else {
        // Fallback without verbose flag
        let simple_clone_output = Command::new("gix")
            .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
            .output()
            .expect("Failed to execute gix clone");

        assert!(
            simple_clone_output.status.success(),
            "Gitoxide clone should succeed: {}",
            String::from_utf8_lossy(&simple_clone_output.stderr)
        );

        println!("Gitoxide basic clone completed successfully");
    }

    Ok(())
}

#[tokio::test]
async fn test_gitoxide_interoperability() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Clone with gitoxide
    let temp_dir = TempDir::new("gitoxide-interop")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let clone_output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    assert!(clone_output.status.success());

    // Test interoperability: use git commands on gitoxide-cloned repo
    let git_status_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "status"])
        .output()
        .expect("Failed to execute git status");

    assert!(
        git_status_output.status.success(),
        "Git should work on gitoxide-cloned repository"
    );

    // Configure git user for commits
    configure_git_user(&clone_path).await?;

    // Create a file and commit using git
    let test_file = clone_path.join("interop-test.txt");
    fs::write(&test_file, "interoperability test")
        .await
        .expect("Failed to write test file");

    let add_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "add",
            "interop-test.txt",
        ])
        .output()
        .expect("Failed to execute git add");

    assert!(add_output.status.success());

    let commit_output = Command::new("git")
        .args(&[
            "-C",
            clone_path.to_str().unwrap(),
            "commit",
            "-m",
            "Interoperability test",
        ])
        .output()
        .expect("Failed to execute git commit");

    assert!(commit_output.status.success());

    // Try to push using git (since gitoxide might not support push yet)
    let push_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "push", "origin", "main"])
        .output()
        .expect("Failed to execute git push");

    assert!(
        push_output.status.success(),
        "Git push should work on gitoxide-cloned repo: {}",
        String::from_utf8_lossy(&push_output.stderr)
    );

    println!("Gitoxide-Git interoperability test completed successfully");
    Ok(())
}

#[tokio::test]
async fn test_gitoxide_repository_validation() -> Result<(), AppError> {
    let config = load_config();
    let repo_identifier = create_random_repo_identifier();

    // Create repository with commits
    create_test_repo_through_api(&repo_identifier, "main").await;
    let setup_repo_path = clone_repo_to_temp(&config.get_full_host(), &repo_identifier).await?;
    create_branch_with_empty_commit_and_push(&setup_repo_path, "main", "Initial commit").await?;

    let remote_url = format!(
        "{}/{}/{}.git",
        config.get_full_host(),
        repo_identifier.owner,
        repo_identifier.repo
    );

    // Clone with gitoxide
    let temp_dir = TempDir::new("gitoxide-validation")
        .await
        .expect("temp dir created");
    let clone_path = temp_dir.path.join("cloned-repo");

    let clone_output = Command::new("gix")
        .args(&["clone", &remote_url, clone_path.to_str().unwrap()])
        .output()
        .expect("Failed to execute gix clone");

    assert!(clone_output.status.success());

    // Test gitoxide repository validation features
    let verify_output = Command::new("gix")
        .args(&["-C", clone_path.to_str().unwrap(), "verify"])
        .output()
        .expect("Failed to execute gix verify");

    if verify_output.status.success() {
        println!("Gitoxide repository verification passed");
    } else {
        // Verify command might not be available in all gitoxide versions
        println!("Gitoxide verify command not available or failed");
    }

    // Test basic repository structure
    assert!(clone_path.join(".git").exists());
    assert!(clone_path.join(".git/config").exists());
    assert!(clone_path.join(".git/HEAD").exists());

    // Verify git compatibility
    let git_fsck_output = Command::new("git")
        .args(&["-C", clone_path.to_str().unwrap(), "fsck"])
        .output()
        .expect("Failed to execute git fsck");

    assert!(
        git_fsck_output.status.success(),
        "Git fsck should pass on gitoxide-cloned repository"
    );

    println!("Repository validation completed successfully");
    Ok(())
}
