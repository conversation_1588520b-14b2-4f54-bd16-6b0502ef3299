[package]
name = "axum-git-server"
version = "0.1.0"
edition = "2024"

[dependencies]
uuid = { version = "1.17.0", features = ["v4"] }
axum = { version = "0.8.4", features = ["macros"] }
gix = "0.72.1"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
tokio = { version = "1.45.1", features = ["full"] }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "json"] }
futures = "0.3.31"
hexdump = "0.1.2"
hex = "0.4.3"
async-stream = "0.3.6"
sha1 = "0.10.6"
rand = "0.9.1"
async-trait = "0.1.88"

[dev-dependencies]
tempfile = "3.20.0"
tower = { version = "0.4", features = ["util"] }
reqwest = { version = "0.12.20", features = ["blocking", "json"] }
git2 = "0.20.2"
