version: 2.1

executors:
  docker-publisher:
    environment:
      IMAGE_TAG: 'armnet/axum-git-server:${CIRCLE_SHA}'
    docker:
      - image: cimg/rust:1.87

defaults: &defaults
  STUFF

commands:
  set_app_image_vars:
    steps:
      - run:
          command: |
            echo "export APP_IMAGE=armnet/axum-git-server:${CIRCLE_SHA1}" >> "$BASH_ENV"
            echo "export APP_IMAGE_LATEST=armnet/axum-git-server:latest" >> "$BASH_ENV"
            echo "export APP_IMAGE_FILE=app-image.tar" >> "$BASH_ENV"
  set_app_init_image_vars:
    steps:
      - run:
          command: |
            echo "export APP_INIT_IMAGE=armnet/axum-git-server-init:${CIRCLE_SHA1}" >> "$BASH_ENV"
            echo "export APP_INIT_IMAGE_LATEST=armnet/axum-git-server-init:latest" >> "$BASH_ENV"
            echo "export APP_INIT_IMAGE_FILE=app-init-image.tar" >> "$BASH_ENV"

jobs:

  build-docker-image-app:
    executor: docker-publisher
    resource_class: medium+

    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - set_app_image_vars
      - run:
          command: docker build -t $APP_IMAGE .
      - run:
          name: Archive Docker image
          command: docker save --output $APP_IMAGE_FILE $APP_IMAGE
      - persist_to_workspace:
          root: .
          paths:
          - ./app-image.tar

  publish-docker-image-app:
    executor: docker-publisher
    resource_class: medium
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - set_app_image_vars
      - attach_workspace:
          at: /tmp/workspace
      - run:
          name: Load Docker image
          command: docker load --input "/tmp/workspace/${APP_IMAGE_FILE}"
      - run:
          name: Publish app docker image
          command: |
            echo "${DOCKERHUB_TOKEN}" | docker login --username "${DOCKERHUB_USER}" --password-stdin
            docker push $APP_IMAGE
            docker tag $APP_IMAGE $APP_IMAGE_LATEST
            docker push $APP_IMAGE_LATEST

  test-service:
    docker:
      - image: cimg/rust:1.87
    resource_class: large
    steps:
      - checkout
      - restore_cache:
          key: cargo-{{ checksum "Cargo.lock" }}
      - run:
          command: cargo check
      - setup_remote_docker
      - run:
          command: docker version
      - run:
          command: docker compose version
      - run:
          command: docker compose build
      - run:
          command: |
            docker compose up -d
            # docker compose exec app bin/./healthcheck.sh
      - run:
          command: docker compose exec app cargo test
      - save_cache:
          key: cargo-{{ checksum "Cargo.lock" }}
          paths:
            - ~/.cargo
workflows:

  test-build-app-images:
    jobs:
      - test-service:
          name: Test service
          # context: github-0
      - build-docker-image-app:
          name: Build app image
      - publish-docker-image-app:
          context: armnet-dockerhub
          requires:
            - Build app image
          filters:
            branches:
              only: master
