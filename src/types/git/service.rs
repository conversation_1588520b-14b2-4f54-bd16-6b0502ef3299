use std::str::FromStr;

#[derive(PartialEq, Eq, C<PERSON>, Debug)]
pub enum GitServiceType {
    UploadPack,
    ReceivePack,
}

impl std::fmt::Display for GitServiceType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            GitServiceType::UploadPack => "git-upload-pack",
            GitServiceType::ReceivePack => "git-receive-pack",
        };
        write!(f, "{}", s)
    }
}

impl FromStr for GitServiceType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "git-upload-pack" => Ok(GitServiceType::UploadPack),
            "git-receive-pack" => Ok(GitServiceType::ReceivePack),
            _ => Err(format!("'{}' is not a valid advertisement service", s)),
        }
    }
}
