/// Git object filters for partial clone support
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum GitFilter {
    /// No blobs - only trees and commits
    BlobNone,
    /// Only blobs smaller than the specified size
    BlobLimit(u64),
    /// No trees or blobs - only commits
    TreeDepth(u32),
    /// Combine multiple filters
    Combine(Vec<GitFilter>),
    /// Sparse checkout filter (path-based)
    Sparse(Vec<String>),
}

impl GitFilter {
    /// Parse filter from Git protocol string
    pub fn parse(filter_str: &str) -> Result<Self, String> {
        match filter_str {
            "blob:none" => Ok(GitFilter::BlobNone),
            s if s.starts_with("blob:limit=") => {
                let size_str = s.strip_prefix("blob:limit=").unwrap();
                let size = size_str.parse::<u64>()
                    .map_err(|_| format!("Invalid blob size: {}", size_str))?;
                Ok(GitFilter::BlobLimit(size))
            }
            s if s.starts_with("tree:") => {
                let depth_str = s.strip_prefix("tree:").unwrap();
                let depth = depth_str.parse::<u32>()
                    .map_err(|_| format!("Invalid tree depth: {}", depth_str))?;
                Ok(GitFilter::TreeDepth(depth))
            }
            s if s.starts_with("combine:") => {
                let filters_str = s.strip_prefix("combine:").unwrap();
                let filter_parts: Vec<&str> = filters_str.split('+').collect();
                let mut filters = Vec::new();
                for part in filter_parts {
                    filters.push(GitFilter::parse(part)?);
                }
                Ok(GitFilter::Combine(filters))
            }
            s if s.starts_with("sparse:") => {
                let paths_str = s.strip_prefix("sparse:").unwrap();
                let paths: Vec<String> = paths_str.split(',').map(|s| s.to_string()).collect();
                Ok(GitFilter::Sparse(paths))
            }
            _ => Err(format!("Unknown filter: {}", filter_str)),
        }
    }

    /// Check if this filter excludes blobs
    pub fn excludes_blobs(&self) -> bool {
        match self {
            GitFilter::BlobNone => true,
            GitFilter::TreeDepth(_) => true,
            GitFilter::Combine(filters) => filters.iter().any(|f| f.excludes_blobs()),
            _ => false,
        }
    }

    /// Check if this filter excludes trees
    pub fn excludes_trees(&self) -> bool {
        match self {
            GitFilter::TreeDepth(0) => true,
            GitFilter::Combine(filters) => filters.iter().any(|f| f.excludes_trees()),
            _ => false,
        }
    }

    /// Get maximum blob size allowed (if any)
    pub fn max_blob_size(&self) -> Option<u64> {
        match self {
            GitFilter::BlobLimit(size) => Some(*size),
            GitFilter::Combine(filters) => {
                filters.iter()
                    .filter_map(|f| f.max_blob_size())
                    .min()
            }
            _ => None,
        }
    }
}

impl std::fmt::Display for GitFilter {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            GitFilter::BlobNone => write!(f, "blob:none"),
            GitFilter::BlobLimit(size) => write!(f, "blob:limit={}", size),
            GitFilter::TreeDepth(depth) => write!(f, "tree:{}", depth),
            GitFilter::Combine(filters) => {
                let filter_strs: Vec<String> = filters.iter().map(|f| f.to_string()).collect();
                write!(f, "combine:{}", filter_strs.join("+"))
            }
            GitFilter::Sparse(paths) => write!(f, "sparse:{}", paths.join(",")),
        }
    }
}

/// Shallow clone options
#[derive(Debug, Clone, PartialEq)]
pub struct ShallowOptions {
    pub depth: Option<u32>,
    pub since: Option<String>,
    pub until: Option<String>,
    pub deepen: Option<u32>,
    pub deepen_since: Option<String>,
    pub deepen_not: Vec<String>,
}

impl Default for ShallowOptions {
    fn default() -> Self {
        Self {
            depth: None,
            since: None,
            until: None,
            deepen: None,
            deepen_since: None,
            deepen_not: Vec::new(),
        }
    }
}

/// Complete fetch request with filtering options
#[derive(Debug, Clone)]
pub struct FetchRequest {
    pub wants: Vec<String>,
    pub haves: Vec<String>,
    pub filter: Option<GitFilter>,
    pub shallow: ShallowOptions,
    pub capabilities: Vec<String>,
    pub done: bool,
}

impl Default for FetchRequest {
    fn default() -> Self {
        Self {
            wants: Vec::new(),
            haves: Vec::new(),
            filter: None,
            shallow: ShallowOptions::default(),
            capabilities: Vec::new(),
            done: false,
        }
    }
}

/// Parse a Git protocol v2 fetch request
pub fn parse_fetch_request(request_body: &[u8]) -> Result<FetchRequest, String> {
    let mut request = FetchRequest::default();
    let request_str = String::from_utf8_lossy(request_body);
    
    for line in request_str.lines() {
        let line = line.trim();
        if line.is_empty() {
            continue;
        }

        if line.starts_with("want ") {
            let oid = line.strip_prefix("want ").unwrap().trim();
            request.wants.push(oid.to_string());
        } else if line.starts_with("have ") {
            let oid = line.strip_prefix("have ").unwrap().trim();
            request.haves.push(oid.to_string());
        } else if line.starts_with("filter ") {
            let filter_str = line.strip_prefix("filter ").unwrap().trim();
            request.filter = Some(GitFilter::parse(filter_str)?);
        } else if line.starts_with("shallow ") {
            let depth_str = line.strip_prefix("shallow ").unwrap().trim();
            request.shallow.depth = Some(depth_str.parse().map_err(|_| "Invalid depth")?);
        } else if line.starts_with("deepen ") {
            let deepen_str = line.strip_prefix("deepen ").unwrap().trim();
            request.shallow.deepen = Some(deepen_str.parse().map_err(|_| "Invalid deepen")?);
        } else if line.starts_with("deepen-since ") {
            let since = line.strip_prefix("deepen-since ").unwrap().trim();
            request.shallow.deepen_since = Some(since.to_string());
        } else if line.starts_with("deepen-not ") {
            let not_ref = line.strip_prefix("deepen-not ").unwrap().trim();
            request.shallow.deepen_not.push(not_ref.to_string());
        } else if line == "done" {
            request.done = true;
        } else if !line.starts_with("command=") && !line.starts_with("agent=") {
            // Treat as capability
            request.capabilities.push(line.to_string());
        }
    }

    Ok(request)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_filter_parsing() {
        assert_eq!(GitFilter::parse("blob:none").unwrap(), GitFilter::BlobNone);
        assert_eq!(GitFilter::parse("blob:limit=1024").unwrap(), GitFilter::BlobLimit(1024));
        assert_eq!(GitFilter::parse("tree:0").unwrap(), GitFilter::TreeDepth(0));
    }

    #[test]
    fn test_filter_properties() {
        assert!(GitFilter::BlobNone.excludes_blobs());
        assert!(!GitFilter::BlobLimit(1024).excludes_blobs());
        assert!(GitFilter::TreeDepth(0).excludes_trees());
        assert_eq!(GitFilter::BlobLimit(1024).max_blob_size(), Some(1024));
    }

    #[test]
    fn test_fetch_request_parsing() {
        let request_body = b"command=fetch\nwant abc123\nhave def456\nfilter blob:none\ndone\n";
        let request = parse_fetch_request(request_body).unwrap();
        
        assert_eq!(request.wants, vec!["abc123"]);
        assert_eq!(request.haves, vec!["def456"]);
        assert_eq!(request.filter, Some(GitFilter::BlobNone));
        assert!(request.done);
    }
}
