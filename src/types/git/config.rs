use std::path::Path<PERSON>uf;
use std::time::Duration;

pub trait GitConfig: Send + Sync {
    fn get_repo_path(&self, owner: &str, repo: &str) -> PathBuf;
    fn use_git_binary_fallback(&self) -> bool;
}

#[derive(<PERSON><PERSON>, Debug)]
pub struct GitConfigImpl {
    pub repos_root: PathBuf,
    pub timeout: Duration,
    pub use_git_binary_fallback: bool,
}

impl GitConfigImpl {
    pub fn init() -> Self {
        let repos_root = std::env::var("REPOS_ROOT").unwrap_or_else(|_| "./repos".to_string());
        let use_git_binary_fallback = std::env::var("USE_GIT_BINARY_FALLBACK")
            .map(|v| v == "true" || v == "1")
            .unwrap_or(false);
        GitConfigImpl {
            repos_root: PathBuf::from(repos_root),
            timeout: Duration::from_secs(30),
            use_git_binary_fallback,
        }
    }
}

impl GitConfig for GitConfigImpl {
    fn get_repo_path(&self, owner: &str, repo: &str) -> PathBuf {
        self.repos_root.join(owner).join(repo)
    }

    fn use_git_binary_fallback(&self) -> bool {
        self.use_git_binary_fallback
    }
}
