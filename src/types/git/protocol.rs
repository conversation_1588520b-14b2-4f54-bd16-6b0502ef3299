use std::str::FromStr;

#[derive(Debug, PartialEq, Eq)]
pub enum GitProtocol {
    V1,
    V2,
}

impl GitProtocol {
    pub fn new(protocol_version: &str) -> Self {
        match GitProtocol::from_str(protocol_version) {
            Ok(r) => r,
            // Can't happen, but the FromStr returns a result so we need to handle the error
            Err(_) => GitProtocol::V1,
        }
    }
}

impl std::fmt::Display for GitProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            GitProtocol::V1 => "version=1",
            GitProtocol::V2 => "version=2",
        };
        write!(f, "{}", s)
    }
}

impl std::str::FromStr for GitProtocol {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "version=2" => Ok(GitProtocol::V2),
            _ => Ok(GitProtocol::V1),
        }
    }
}
