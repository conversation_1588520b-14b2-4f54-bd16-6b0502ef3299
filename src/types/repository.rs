#[derive(<PERSON><PERSON>, Debug)]
pub struct RepoIdentifier {
    pub owner: String,
    pub repo: String,
}

impl RepoIdentifier {
    pub fn new(owner: String, repo: String) -> Self {
        Self { owner, repo }
    }

    pub fn from_path_parts(owner: &str, repo: &str) -> Self {
        Self {
            owner: owner.to_string(),
            repo: repo.to_string(),
        }
    }

    // pub fn path_exists(&self) -> Result<(), AppError> {
    //     let repo_path =
    //     if
    // }
}
