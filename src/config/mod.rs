#[derive(<PERSON><PERSON>, Debug)]
pub struct AppConfig {
    pub service_port: u16,
    pub service_host: String,
    pub secure_host: bool,
    pub max_body_size: usize,
}

impl AppConfig {
    pub fn get_full_host(&self) -> String {
        let protocol = if self.secure_host { "https" } else { "http" };
        format!("{}://{}:{}", protocol, self.service_host, self.service_port)
    }
}

pub fn load_config() -> AppConfig {
    AppConfig {
        service_port: match std::env::var("SERVICE_PORT") {
            Ok(port) => port
                .parse()
                .expect("SERVICE_PORT to be a valid port number"),
            Err(_) => 5500,
        },
        service_host: match std::env::var("SERVICE_HOST") {
            Ok(host) => host,
            Err(_) => "0.0.0.0".to_string(),
        },
        secure_host: match std::env::var("SECURE_HOST") {
            Ok(host) => host.parse().unwrap_or(false),
            Err(_) => false,
        },
        max_body_size: match std::env::var("MAX_BODY_SIZE") {
            Ok(size) => size
                .parse()
                .expect("MAX_BODY_SIZE to be a valid number in bytes"),
            Err(_) => 500 * 1024 * 1024, // Default: 500MB
        },
    }
}
