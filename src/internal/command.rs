use std::{path::PathBuf, process::Stdio, time::Duration};
use tokio::{io::AsyncWriteExt, process::Command, time::timeout};

use crate::error::AppError;

pub async fn run_command(
    cmd_str: &str,
    args: &[&str],
    cmd_dir: Option<&PathBuf>,
    input: Option<&[u8]>,
) -> Result<Vec<u8>, AppError> {
    let mut cmd = Command::new(cmd_str);

    let debug_cmd_str = format!("{} {}", cmd_str, args.join(" "));
    tracing::debug!("Running command: {}", debug_cmd_str);

    cmd.args(args).stdout(Stdio::piped()).stderr(Stdio::piped());

    if input.is_some() {
        cmd.stdin(Stdio::piped());
    }

    if let Some(dir) = cmd_dir {
        cmd.current_dir(dir);
    }

    let mut child = cmd.spawn()?;

    if let Some(input) = input {
        if let Some(mut stdin) = child.stdin.take() {
            stdin.write_all(input).await?;
        }
    }

    match timeout(Duration::from_secs(200), child.wait_with_output()).await {
        Ok(Ok(output)) => {
            if output.status.success() {
                Ok(output.stdout)
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                tracing::error!("Command failed: {}", stderr);
                Err(AppError::InternalServerError {
                    internal: stderr.to_string(),
                })
            }
        }
        Ok(Err(e)) => {
            tracing::error!("Failed to run command: {}", e);
            Err(AppError::InternalServerError {
                internal: e.to_string(),
            })
        }
        Err(_) => {
            tracing::error!("Command timed out: {}", debug_cmd_str);
            Err(AppError::InternalServerError {
                internal: "Command timed out".to_string(),
            })
        }
    }
}
