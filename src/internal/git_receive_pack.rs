use std::path::Path;

use crate::{
    error::AppError,
    helpers::git_pkt::{parse_pkt_line, write_flush_packet, write_packet_line},
};

/// Git receive-pack protocol implementation
///
/// This module implements the complete Git receive-pack (push) protocol:
/// 1. Parse reference update requests from client
/// 2. Validate reference updates
/// 3. Process pack-file data
/// 4. Update references
/// 5. Execute hooks (pre-receive, update, post-receive)
/// 6. Send status report back to client

#[derive(Debug, <PERSON>lone)]
pub struct RefUpdate {
    pub old_oid: String,
    pub new_oid: String,
    pub ref_name: String,
}

#[derive(Debug, <PERSON>lone)]
pub struct ReceivePackRequest {
    pub ref_updates: Vec<RefUpdate>,
    pub capabilities: Vec<String>,
    pub pack_data: Option<Vec<u8>>,
}

#[derive(Debug, Clone)]
pub enum RefUpdateStatus {
    Ok,
    Error(String),
}

#[derive(Debug, <PERSON>lone)]
pub struct RefUpdateResult {
    pub ref_name: String,
    pub status: RefUpdateStatus,
}

/// Parse a receive-pack request from the client
pub fn parse_receive_pack_request(request_body: &[u8]) -> Result<ReceivePackRequest, AppError> {
    let mut ref_updates = Vec::new();
    let mut capabilities = Vec::new();
    let mut pack_data = None;
    let mut pos = 0;

    // Handle empty request body (e.g., "Everything up-to-date" case)
    if request_body.is_empty() {
        tracing::info!("Empty request body - no updates needed");
        return Ok(ReceivePackRequest {
            ref_updates,
            capabilities,
            pack_data,
        });
    }

    // Parse packet lines until we hit the pack data or end
    while pos < request_body.len() {
        // Check if we have enough bytes for a packet line header
        if request_body.len() - pos < 4 {
            // Not enough bytes for a packet line header, treat as pack data
            pack_data = Some(request_body[pos..].to_vec());
            break;
        }

        // Try to parse packet line, but handle the case where we hit pack data
        let (pkt_len, pkt_data) = match parse_pkt_line(&request_body[pos..]) {
            Ok(result) => result,
            Err(_) => {
                // Failed to parse packet line, assume we've hit pack data
                pack_data = Some(request_body[pos..].to_vec());
                break;
            }
        };

        if pkt_len == 0 {
            // Flush packet - end of ref updates, pack data follows
            pos += 4;
            if pos < request_body.len() {
                pack_data = Some(request_body[pos..].to_vec());
            }
            break;
        }

        let line = String::from_utf8_lossy(pkt_data);
        let line = line.trim();

        if line.is_empty() {
            pos += pkt_len;
            continue;
        }

        // Parse ref update line: "old_oid new_oid ref_name [capabilities]"
        // First, check if this line contains capabilities (indicated by null byte)
        let (ref_line, caps_part) = if let Some(null_pos) = line.find('\0') {
            let ref_part = &line[..null_pos];
            let caps_part = &line[null_pos + 1..];
            (ref_part, Some(caps_part))
        } else {
            (line, None)
        };

        let parts: Vec<&str> = ref_line.split_whitespace().collect();
        if parts.len() < 3 {
            pos += pkt_len;
            continue;
        }

        let old_oid = parts[0].to_string();
        let new_oid = parts[1].to_string();
        let ref_name = parts[2].to_string();

        // Extract capabilities if this is the first ref update and capabilities are present
        if ref_updates.is_empty() {
            if let Some(caps_str) = caps_part {
                capabilities = caps_str.split_whitespace().map(|s| s.to_string()).collect();
            }
        }

        ref_updates.push(RefUpdate {
            old_oid,
            new_oid,
            ref_name,
        });

        pos += pkt_len;
    }

    Ok(ReceivePackRequest {
        ref_updates,
        capabilities,
        pack_data,
    })
}

/// Process a receive-pack request and return status report
pub async fn process_receive_pack_request(
    repo_path: &Path,
    request: ReceivePackRequest,
) -> Result<Vec<u8>, AppError> {
    let mut results = Vec::new();

    // Process pack file if present
    if let Some(pack_data) = &request.pack_data {
        process_pack_file(repo_path, pack_data).await?;
    }

    // Process each reference update
    for ref_update in &request.ref_updates {
        let result = process_ref_update(repo_path, ref_update).await;
        results.push(result);
    }

    // Generate status report
    generate_status_report(&results, &request.capabilities)
}

/// Process pack file data
async fn process_pack_file(repo_path: &Path, pack_data: &[u8]) -> Result<(), AppError> {
    // For now, delegate pack processing to git
    // TODO: Implement native pack file processing
    let mut cmd = tokio::process::Command::new("git");
    tracing::info!("Processing pack files in {:?}", repo_path);
    cmd.arg("unpack-objects")
        .arg("--strict")
        .current_dir(repo_path)
        .stdin(std::process::Stdio::piped())
        .stdout(std::process::Stdio::piped())
        .stderr(std::process::Stdio::piped());

    let mut child = cmd.spawn().map_err(|e| AppError::GitAdvertisement {
        public: "Failed to process pack file".to_string(),
        internal: Some(format!("Failed to spawn git unpack-objects: {}", e)),
    })?;

    // Write pack data to stdin
    if let Some(stdin) = child.stdin.take() {
        use tokio::io::AsyncWriteExt;
        let mut stdin = stdin;
        stdin
            .write_all(pack_data)
            .await
            .map_err(|e| AppError::GitAdvertisement {
                public: "Failed to process pack file".to_string(),
                internal: Some(format!("Failed to write pack data: {}", e)),
            })?;
    }

    let output = child
        .wait_with_output()
        .await
        .map_err(|e| AppError::GitAdvertisement {
            public: "Failed to process pack file".to_string(),
            internal: Some(format!("Failed to wait for git unpack-objects: {}", e)),
        })?;

    if !output.status.success() {
        return Err(AppError::GitAdvertisement {
            public: "Failed to process pack file".to_string(),
            internal: Some(format!(
                "git unpack-objects failed: {}",
                String::from_utf8_lossy(&output.stderr)
            )),
        });
    }

    Ok(())
}

/// Process a single reference update
async fn process_ref_update(repo_path: &Path, ref_update: &RefUpdate) -> RefUpdateResult {
    // Validate the reference update
    if let Err(e) = validate_ref_update(repo_path, ref_update) {
        return RefUpdateResult {
            ref_name: ref_update.ref_name.clone(),
            status: RefUpdateStatus::Error(e),
        };
    }

    // Apply the reference update
    match apply_ref_update(repo_path, ref_update).await {
        Ok(()) => RefUpdateResult {
            ref_name: ref_update.ref_name.clone(),
            status: RefUpdateStatus::Ok,
        },
        Err(e) => RefUpdateResult {
            ref_name: ref_update.ref_name.clone(),
            status: RefUpdateStatus::Error(e),
        },
    }
}

/// Validate a reference update
fn validate_ref_update(repo_path: &Path, ref_update: &RefUpdate) -> Result<(), String> {
    // Check if it's a deletion (new_oid is all zeros)
    let is_deletion = ref_update.new_oid == "0000000000000000000000000000000000000000";

    // Check if it's a creation (old_oid is all zeros)
    let is_creation = ref_update.old_oid == "0000000000000000000000000000000000000000";

    // Open repository for validation
    let repo = gix::open(repo_path).map_err(|e| format!("Failed to open repo: {}", e))?;

    if is_deletion {
        // Deletion: verify the reference exists and old_oid matches
        if let Ok(current_ref) = repo.find_reference(&ref_update.ref_name) {
            if let Ok(current_oid) = current_ref.into_fully_peeled_id() {
                if current_oid.to_string() != ref_update.old_oid {
                    return Err("deletion refused: old object id does not match".to_string());
                }
            }
        } else {
            return Err("deletion refused: reference does not exist".to_string());
        }
    } else if !is_creation {
        // Update: verify old_oid matches current ref
        if let Ok(current_ref) = repo.find_reference(&ref_update.ref_name) {
            if let Ok(current_oid) = current_ref.into_fully_peeled_id() {
                if current_oid.to_string() != ref_update.old_oid {
                    return Err("non-fast-forward".to_string());
                }
            }
        }
    }

    // Validate new OID format (for non-deletions)
    if !is_deletion && gix::ObjectId::from_hex(ref_update.new_oid.as_bytes()).is_err() {
        return Err("invalid object id".to_string());
        // Note: We don't check if object exists here because it might be in the pack file
        // that hasn't been processed yet. The git update-ref command will validate existence.
    }

    Ok(())
}

/// Apply a reference update
async fn apply_ref_update(repo_path: &Path, ref_update: &RefUpdate) -> Result<(), String> {
    let is_deletion = ref_update.new_oid == "0000000000000000000000000000000000000000";

    if is_deletion {
        // Delete reference using git update-ref -d
        let output = tokio::process::Command::new("git")
            .args(["update-ref", "-d", &ref_update.ref_name])
            .current_dir(repo_path)
            .output()
            .await
            .map_err(|e| format!("failed to delete ref: {}", e))?;

        if !output.status.success() {
            return Err(format!(
                "git update-ref -d failed: {}",
                String::from_utf8_lossy(&output.stderr)
            ));
        }
    } else {
        // Create or update reference
        let _new_oid = gix::ObjectId::from_hex(ref_update.new_oid.as_bytes())
            .map_err(|e| format!("invalid object id: {}", e))?;

        // TODO: Implement reference creation/update
        // For now, use git update-ref command
        let output = tokio::process::Command::new("git")
            .args(["update-ref", &ref_update.ref_name, &ref_update.new_oid])
            .current_dir(repo_path)
            .output()
            .await
            .map_err(|e| format!("failed to update ref: {}", e))?;

        if !output.status.success() {
            return Err(format!(
                "git update-ref failed: {}",
                String::from_utf8_lossy(&output.stderr)
            ));
        }
    }

    Ok(())
}

/// Generate status report for client
///
/// This function generates the response that Git clients expect after a receive-pack operation.
/// The format depends on whether the client negotiated sideband capability.
///
/// ## Protocol Details
///
/// ### Without Sideband (simple case):
/// ```text
/// PKT-LINE("unpack ok")
/// PKT-LINE("ok refs/heads/branch-name")
/// PKT-LINE(flush)
/// ```
///
/// ### With Sideband (complex case):
/// When the client negotiates `side-band-64k` capability, the status report must be sent
/// through sideband channel 1 (pack data) with nested packet lines:
///
/// ```text
/// PKT-LINE(
///   SIDEBAND_CHANNEL_1 +
///   PKT-LINE("unpack ok") +
///   PKT-LINE("ok refs/heads/branch-name") +
///   PKT-LINE(flush)
/// )
/// PKT-LINE(flush)
/// PKT-LINE(flush)  // Double flush for proper termination
/// ```
///
/// ## Key Insights from Protocol Analysis
///
/// Through byte-level comparison with the official git binary, we discovered:
///
/// 1. **Sideband Channel**: Status reports go through channel 1 (pack data), not channel 2 (progress)
/// 2. **Nested Structure**: Status reports are nested as regular packet lines inside sideband packets
/// 3. **Termination**: Double flush packets are required for proper stream termination
///
/// ### Example Response (Hex):
/// ```text
/// Git Binary:    303033380130303065756e7061636b206f6b0a303032316f6b20726566732f68656164732f666561747572652d6272616e63680a3030303030303030
/// Our Response:  303033380130303065756e7061636b206f6b0a303032316f6b20726566732f68656164732f666561747572652d6272616e63680a303030303030303030303030
///
/// Decoded:
/// - 0038: Outer packet length (56 bytes)
/// - 01: Sideband channel 1
/// - 000e: Inner packet length (14 bytes)
/// - "unpack ok\n": Status message
/// - 0021: Inner packet length (33 bytes)
/// - "ok refs/heads/feature-branch\n": Ref status
/// - 0000: Inner flush packet
/// - 0000: Outer flush packet
/// - 0000: Final flush packet (our implementation adds extra)
/// ```
fn generate_status_report(
    results: &[RefUpdateResult],
    capabilities: &[String],
) -> Result<Vec<u8>, AppError> {
    let mut buf = Vec::new();

    // Check if client supports report-status and sideband
    let report_status = capabilities.iter().any(|cap| cap == "report-status");
    let use_sideband = capabilities
        .iter()
        .any(|cap| cap == "side-band-64k" || cap == "side-band");

    if report_status {
        if use_sideband {
            // CRITICAL: When sideband is negotiated, status reports must be sent through
            // sideband channel 1 (pack data) with nested packet lines.
            // This was discovered through byte-level analysis of git binary responses.

            // Step 1: Build the nested packet lines for status report
            // These will be wrapped inside a sideband packet
            let mut nested_packets = Vec::new();

            // Add unpack status as nested packet line
            let unpack_line = "unpack ok\n";
            write_packet_line(&mut nested_packets, unpack_line.as_bytes()).map_err(|e| {
                AppError::GitAdvertisement {
                    public: "Failed to write nested status packet".to_string(),
                    internal: Some(format!("Write error: {}", e)),
                }
            })?;

            // Add status for each ref update as nested packet lines
            for result in results {
                let status_line = match &result.status {
                    RefUpdateStatus::Ok => format!("ok {}\n", result.ref_name),
                    RefUpdateStatus::Error(msg) => format!("ng {} {}\n", result.ref_name, msg),
                };
                write_packet_line(&mut nested_packets, status_line.as_bytes()).map_err(|e| {
                    AppError::GitAdvertisement {
                        public: "Failed to write nested status packet".to_string(),
                        internal: Some(format!("Write error: {}", e)),
                    }
                })?;
            }

            // Step 2: Add nested flush packet to terminate the inner packet stream
            write_flush_packet(&mut nested_packets).map_err(|e| AppError::GitAdvertisement {
                public: "Failed to write nested flush packet".to_string(),
                internal: Some(format!("Write error: {}", e)),
            })?;

            // Step 3: Wrap the nested packets in sideband channel 1
            // Format: PKT-LINE(CHANNEL_BYTE + nested_packet_data)
            let mut sideband_data = vec![1u8]; // Sideband channel 1 (pack data)
            sideband_data.extend_from_slice(&nested_packets);
            write_packet_line(&mut buf, &sideband_data).map_err(|e| {
                AppError::GitAdvertisement {
                    public: "Failed to write sideband status report".to_string(),
                    internal: Some(format!("Write error: {}", e)),
                }
            })?;
        } else {
            // When no sideband, send status report as regular packet lines
            let unpack_line = "unpack ok";
            write_packet_line(&mut buf, unpack_line.as_bytes()).map_err(|e| {
                AppError::GitAdvertisement {
                    public: "Failed to write status report".to_string(),
                    internal: Some(format!("Write error: {}", e)),
                }
            })?;

            for result in results {
                let status_line = match &result.status {
                    RefUpdateStatus::Ok => format!("ok {}", result.ref_name),
                    RefUpdateStatus::Error(msg) => format!("ng {} {}", result.ref_name, msg),
                };
                write_packet_line(&mut buf, status_line.as_bytes()).map_err(|e| {
                    AppError::GitAdvertisement {
                        public: "Failed to write status report".to_string(),
                        internal: Some(format!("Write error: {}", e)),
                    }
                })?;
            }
        }
    }

    // Step 4: Always end with double flush packet like git binary does
    // This ensures proper stream termination that Git clients expect
    write_flush_packet(&mut buf).map_err(|e| AppError::GitAdvertisement {
        public: "Failed to write first flush packet".to_string(),
        internal: Some(format!("Write error: {}", e)),
    })?;
    write_flush_packet(&mut buf).map_err(|e| AppError::GitAdvertisement {
        public: "Failed to write second flush packet".to_string(),
        internal: Some(format!("Write error: {}", e)),
    })?;

    Ok(buf)
}
