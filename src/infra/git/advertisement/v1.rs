use crate::{
    domain::git_advertisement_generator::GitV1AdvertisementGenerator,
    error::AppError,
    helpers::{
        git_pkt::{write_flush_packet, write_packet_line},
        gix as gix_helpers, // Might need to rename
    },
    types::git::{config::GitConfig, service::GitServiceType},
    types::repository::RepoIdentifier,
};
use async_trait::async_trait;
use std::sync::Arc;

pub struct GitV1AdvertisementGeneratorImpl {
    git_config: Arc<dyn GitConfig>,
}

impl GitV1AdvertisementGeneratorImpl {
    pub fn new(git_config: Arc<dyn GitConfig>) -> Self {
        Self { git_config }
    }
}

#[async_trait]
impl GitV1AdvertisementGenerator for GitV1AdvertisementGeneratorImpl {
    async fn generate_upload_pack_advertisement(
        &self,
        repo: &RepoIdentifier,
    ) -> Result<Vec<u8>, AppError> {
        // probably we can directly generate from here
        let advertiser = GitAdvertisementV1::new(GitServiceType::UploadPack);
        let repo_path = self.git_config.get_repo_path(&repo.owner, &repo.repo);
        let gix_repo = gix_helpers::open_repo(&repo_path)?;
        let buf = advertiser.generate_body(&gix_repo)?;

        Ok(buf)
    }

    async fn generate_receive_pack_advertisement(
        &self,
        repo: &RepoIdentifier,
    ) -> Result<Vec<u8>, AppError> {
        let advertiser = GitAdvertisementV1::new(GitServiceType::ReceivePack);
        let repo_path = self.git_config.get_repo_path(&repo.owner, &repo.repo);
        let gix_repo = gix_helpers::open_repo(&repo_path)?;
        let buf = advertiser.generate_body(&gix_repo)?;
        Ok(buf)
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum GitAdvertisementV1 {
    UploadPack,
    ReceivePack,
}

impl GitAdvertisementV1 {
    pub fn new(service: GitServiceType) -> Self {
        match service {
            GitServiceType::UploadPack => Self::UploadPack,
            GitServiceType::ReceivePack => Self::ReceivePack,
        }
    }

    /// Get the service type for this advertisement
    pub fn service_type(&self) -> GitServiceType {
        match self {
            Self::UploadPack => GitServiceType::UploadPack,
            Self::ReceivePack => GitServiceType::ReceivePack,
        }
    }

    pub fn generate_body(&self, repo: &gix::Repository) -> Result<Vec<u8>, AppError> {
        let mut buf = Vec::new();

        // Write service announcement
        let service_line = format!("# service={}\n", self.service_type());
        write_packet_line(&mut buf, service_line.as_bytes()).map_err(|e| {
            AppError::GitAdvertisement {
                public: "Failed to generate advertisement".to_string(),
                internal: Some(format!("Failed to write service line: {}", e)),
            }
        })?;

        write_flush_packet(&mut buf).map_err(|e| AppError::GitAdvertisement {
            public: "Failed to generate advertisement".to_string(),
            internal: Some(format!("Failed to write flush packet: {}", e)),
        })?;

        // Generate protocol-specific content
        match self {
            Self::UploadPack => self.generate_body_with_refs(&mut buf, repo)?,
            Self::ReceivePack => self.generate_body_with_refs(&mut buf, repo)?,
        }

        // Final flush packet
        write_flush_packet(&mut buf).map_err(|e| AppError::GitAdvertisement {
            public: "Failed to generate advertisement".to_string(),
            internal: Some(format!("Failed to write final flush packet: {}", e)),
        })?;

        Ok(buf)
    }

    fn generate_body_with_refs(
        &self,
        buf: &mut Vec<u8>,
        repo: &gix::Repository,
    ) -> Result<(), AppError> {
        let (head_oid_string, head_symref) = resolve_head_reference(repo)?;

        let mut capabilities = match self.service_type() {
            GitServiceType::UploadPack => self.generate_upload_pack_capabilities(),
            GitServiceType::ReceivePack => self.generate_receive_pack_capabilities(),
        };
        if let Some(oid_str) = head_oid_string {
            let mut line = format!("{} HEAD", oid_str);
            line.push('\0');

            if let Some(symref) = head_symref {
                if !capabilities.contains("symref=") {
                    capabilities.push_str(&format!(" {}", symref));
                }
            }

            line.push_str(&capabilities);
            line.push('\n');
            write_packet_line(buf, line.as_bytes()).map_err(|e| AppError::GitAdvertisement {
                public: "Failed to generate git advertisement".to_string(),
                internal: Some(format!(
                    "Failed to write HEAD line to v1 upload pack advertisement: {}",
                    e
                )),
            })?;
        } else {
            let mut line_bytes = Vec::new();
            line_bytes
                .extend_from_slice(b"0000000000000000000000000000000000000000 capabilities^{}");
            line_bytes.push(0u8);

            // Generate capabilities with symref info if available
            if let Some(symref) = head_symref {
                if !capabilities.contains("symref=") {
                    capabilities.push_str(&format!(" {}", symref));
                }
            }

            line_bytes.extend_from_slice(capabilities.as_bytes());
            line_bytes.push(b'\n');
            write_packet_line(buf, &line_bytes).map_err(|e| AppError::GitAdvertisement {
                public: "Failed to generate git advertisement".to_string(),
                internal: Some(format!(
                    "Failed to write capabilities line for v1 upload pack: {}",
                    e
                )),
            })?;
        }

        // Write other references
        self.write_repository_refs(buf, repo)?;

        Ok(())
    }

    /// Write repository references (excluding HEAD)
    fn write_repository_refs(
        &self,
        buf: &mut Vec<u8>,
        repo: &gix::Repository,
    ) -> Result<(), AppError> {
        let platform = repo.refs.iter().map_err(|e| AppError::GitAdvertisement {
            public: "Failed to read repository references".to_string(),
            internal: Some(format!("Failed to create refs iterator: {}", e)),
        })?;

        let refs = platform.all().map_err(|e| AppError::GitAdvertisement {
            public: "Failed to read repository references".to_string(),
            internal: Some(format!("Failed to get all refs: {}", e)),
        })?;

        for r in refs {
            let reference = match r {
                Ok(r) => r,
                Err(e) => {
                    tracing::error!("Failed to read a reference: {:?}", e);
                    continue; // Skip this reference but continue with others
                }
            };

            let name = reference.name.as_bstr();
            let target = reference.target;

            if name == "HEAD" {
                continue;
            }

            let line = format!("{} {}\n", target, name);

            write_packet_line(buf, line.as_bytes()).map_err(|e| AppError::GitAdvertisement {
                public: "Failed to generate advertisement".to_string(),
                internal: Some(format!("Failed to write ref '{}': {}", name, e)),
            })?;
        }

        Ok(())
    }

    fn generate_upload_pack_capabilities(&self) -> String {
        "multi_ack thin-pack side-band side-band-64k ofs-delta shallow deepen-since deepen-not deepen-relative no-progress include-tag multi_ack_detailed allow-tip-sha1-in-want allow-reachable-sha1-in-want no-done filter"
            .to_string()
    }

    fn generate_receive_pack_capabilities(&self) -> String {
        "report-status delete-refs side-band-64k quiet atomic ofs-delta push-options".to_string()
    }
}

fn resolve_head_reference(
    repo: &gix::Repository,
) -> Result<(Option<String>, Option<String>), AppError> {
    if let Ok(head_ref) = repo.find_reference("HEAD") {
        // Try different approaches to resolve HEAD
        match head_ref.target() {
            gix::refs::TargetRef::Symbolic(target_name) => {
                // HEAD is symbolic, try to resolve the target
                let symref_info = format!("symref=HEAD:{}", target_name.as_bstr());

                // Try to resolve the target branch to get its OID
                if let Ok(target_ref) = repo.find_reference(target_name.as_bstr()) {
                    match target_ref.target() {
                        gix::refs::TargetRef::Object(oid) => {
                            Ok((Some(oid.to_string()), Some(symref_info)))
                        }
                        _ => {
                            tracing::warn!("Target reference is not a direct object reference");
                            Ok((None, Some(symref_info)))
                        }
                    }
                } else {
                    // This is normal for new repositories - the branch doesn't exist until first commit
                    Ok((None, Some(symref_info)))
                }
            }
            gix::refs::TargetRef::Object(oid) => {
                // HEAD points directly to an object
                Ok((Some(oid.to_string()), None))
            }
        }
    } else {
        tracing::warn!("Could not find HEAD reference");
        Ok((None, None))
    }
}
