use async_trait::async_trait;

use crate::{
    domain::git_advertisement_generator::GitV2AdvertisementGenerator,
    error::AppError,
    helpers::git_pkt::{write_flush_packet, write_packet_line},
    types::git::service::GitServiceType,
};

pub struct GitV2AdvertisementGeneratorImpl {}

impl Default for GitV2AdvertisementGeneratorImpl {
    fn default() -> Self {
        Self::new()
    }
}

impl GitV2AdvertisementGeneratorImpl {
    pub fn new() -> Self {
        Self {}
    }

    fn generate_service_advertisement_lines(
        &self,
        buf: &mut Vec<u8>,
        service: &GitServiceType,
    ) -> Result<(), AppError> {
        let service_line = format!("# service={}\n", service);
        write_packet_line(buf, service_line.as_bytes()).map_err(|e| {
            AppError::GitAdvertisement {
                public: "Failed to generate advertisement".to_string(),
                internal: Some(format!("Failed to write service line: {}", e)),
            }
        })?;

        write_flush_packet(buf).map_err(|e| AppError::GitAdvertisement {
            public: "Failed to generate advertisement".to_string(),
            internal: Some(format!("Failed to write flush packet: {}", e)),
        })?;
        Ok(())
    }

    fn generate_final_flush_packet(&self, buf: &mut Vec<u8>) -> Result<(), AppError> {
        write_flush_packet(buf).map_err(|e| AppError::GitAdvertisement {
            public: "Failed to generate advertisement".to_string(),
            internal: Some(format!("Failed to write final flush packet: {}", e)),
        })?;
        Ok(())
    }

    fn generate_capabilities(&self, service: &GitServiceType) -> Vec<String> {
        let mut capabilities = vec!["agent=git/rust-git-server".to_string()];

        match service {
            GitServiceType::UploadPack => {
                capabilities.extend([
                    "ls-refs=unborn".to_string(),
                    "fetch=shallow wait-for-done filter".to_string(),
                    "server-option".to_string(),
                    "object-format=sha1".to_string(),
                ]);
            }
            GitServiceType::ReceivePack => {
                capabilities.extend([
                    "push-options".to_string(),
                    "atomic".to_string(),
                    "report-status".to_string(),
                    "report-status-v2".to_string(),
                    "delete-refs".to_string(),
                    "quiet".to_string(),
                    "push-cert".to_string(),
                    "side-band-64".to_string(),
                    "object-format=sha1".to_string(),
                ]);
            }
        }

        capabilities
    }

    fn generate_v2_body(
        &self,
        buf: &mut Vec<u8>,
        service: &GitServiceType,
    ) -> Result<(), AppError> {
        write_packet_line(buf, b"version 2\n").map_err(|e| AppError::GitAdvertisement {
            public: "Failed to generate advertisement".to_string(),
            internal: Some(format!("Failed to write version line: {}", e)),
        })?;

        // Send capabilities for v2
        let v2_capabilities = self.generate_capabilities(service);
        for capability in v2_capabilities {
            write_packet_line(buf, format!("{}\n", capability).as_bytes()).map_err(|e| {
                AppError::GitAdvertisement {
                    public: "Failed to generate advertisement".to_string(),
                    internal: Some(format!(
                        "Failed to write capability '{}': {}",
                        capability, e
                    )),
                }
            })?;
        }

        Ok(())
    }
}

#[async_trait]
impl GitV2AdvertisementGenerator for GitV2AdvertisementGeneratorImpl {
    async fn generate_upload_pack_advertisement(&self) -> Result<Vec<u8>, AppError> {
        let mut buf = Vec::new();
        let service = GitServiceType::UploadPack;
        self.generate_service_advertisement_lines(&mut buf, &service)?;
        self.generate_v2_body(&mut buf, &service)?;

        self.generate_final_flush_packet(&mut buf)?;
        Ok(buf)
    }

    async fn generate_receive_pack_advertisement(&self) -> Result<Vec<u8>, AppError> {
        let mut buf = Vec::new();
        let service = GitServiceType::ReceivePack;

        self.generate_service_advertisement_lines(&mut buf, &service)?;
        self.generate_v2_body(&mut buf, &service)?;

        self.generate_final_flush_packet(&mut buf)?;
        Ok(buf)
    }
}
