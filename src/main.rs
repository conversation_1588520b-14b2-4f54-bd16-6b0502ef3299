use axum::{
    Router,
    extract::DefaultBodyLimit,
    middleware,
    routing::{get, post},
};
use std::time::Instant;
use std::{net::SocketAddr, sync::Arc};
use tokio::signal;

use axum_git_server::{
    api::{
        middlewares::request::{measure_req, set_req_id},
        routes::{
            git_advertisement::handle_git_advertisement, git_receive_pack::handle_git_receive_pack,
            git_upload_pack::handle_git_upload_pack, repository::handle_create_repository,
        },
    },
    config::load_config,
    domain::DomainContext,
    types::state::AppState,
};

#[tokio::main]
async fn main() {
    let start_time = Instant::now();
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .json()
        .init();

    let app_config = load_config();

    let state = Arc::new(AppState {
        app_config: app_config.clone(),
        domain: DomainContext::new(),
    });

    let http_addr = SocketAddr::from(([0, 0, 0, 0], app_config.service_port));

    let app = Router::new()
        .route("/", get(|| async { "Hello, Axum!" }))
        .route("/healthcheck", get(|| async { "OK" }))
        .route("/{owner}/{repo}/info/refs", get(handle_git_advertisement))
        .route(
            "/{owner}/{repo}/git-upload-pack",
            post(handle_git_upload_pack),
        )
        .route(
            "/{owner}/{repo}/git-receive-pack",
            post(handle_git_receive_pack),
        )
        .route("/repositories", post(handle_create_repository))
        .layer(DefaultBodyLimit::max(app_config.max_body_size)) // Configurable body size limit for Git pack files
        .layer(middleware::from_fn(measure_req))
        .layer(middleware::from_fn(set_req_id))
        .with_state(state);

    tracing::info!("Axum Git server listening on {}", &http_addr);

    let axum_listener = tokio::net::TcpListener::bind(http_addr)
        .await
        .expect("Create TCP listener for server");

    tracing::info!("Axum listening on {}", &http_addr);
    axum::serve(axum_listener, app)
        .with_graceful_shutdown(shutdown_signal(start_time))
        .await
        .unwrap_or_else(|e| panic!("Failed to start server on {}. Err: {}", &http_addr, e))
}

async fn shutdown_signal(start_time: Instant) {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    tracing::info!(
        elapsed_ms = start_time.elapsed().as_millis(),
        elapsed_s = start_time.elapsed().as_secs(),
        "Terminate signal received. Server alive for {:?}",
        start_time.elapsed()
    );
}
