use axum::http::{HeaderMap, HeaderValue, header};

use crate::types::git::protocol::GitProtocol;

// Might return a result in the future.
// For now we try to construct the response headers with defaults in case of failures
pub async fn create_git_response_headers(
    req_headers: &HeaderMap,
    service: &str,
    protocol_version: &GitProtocol,
) -> HeaderMap {
    let mut headers = HeaderMap::new();
    let user_agent = req_headers
        .get(header::USER_AGENT)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    let protocol_version = HeaderValue::from_str(&protocol_version.to_string())
        .unwrap_or(HeaderValue::from_static("version=1"));

    headers.insert(
        header::USER_AGENT,
        HeaderValue::from_str(user_agent).unwrap_or(HeaderValue::from_static("invalid-user-agent")),
    );
    headers.insert("Git-Protocol", protocol_version);
    headers.insert(header::CACHE_CONTROL, HeaderValue::from_static("no-cache"));
    headers.insert(header::PRAGMA, HeaderValue::from_static("no-cache"));
    headers.insert(
        header::CONTENT_TYPE,
        HeaderValue::from_str(&format!("application/x-{}-advertisement", service)).unwrap_or(
            HeaderValue::from_static("application/x-git-upload-pack-advertisement"),
        ),
    );
    headers
}
