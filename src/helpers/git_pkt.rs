use crate::error::AppError;
use hexdump::hexdump;
use std::io;

pub fn write_packet_line<W: io::Write>(writer: &mut W, data: &[u8]) -> io::Result<()> {
    // Calculate the total length (payload + 4 bytes for the length prefix)
    let length = data.len() + 4;
    if length > 0xFFFF {
        return Err(io::Error::new(
            io::ErrorKind::InvalidInput,
            "Packet too long",
        ));
    }
    // Write the length prefix
    write!(writer, "{:04x}", length)?;
    // Write the payload
    writer.write_all(data)?;
    Ok(())
}

pub fn write_delimiter_packet<W: io::Write>(mut writer: W) -> std::io::Result<()> {
    writer.write_all(b"0001")
}

pub fn write_flush_packet<W: io::Write>(mut writer: W) -> std::io::Result<()> {
    writer.write_all(b"0000")
}

/// Parse a packet line from bytes
/// Returns (packet_length, packet_data) where packet_length includes the 4-byte header
pub fn parse_pkt_line(data: &[u8]) -> Result<(usize, &[u8]), io::Error> {
    if data.len() < 4 {
        return Err(io::Error::new(
            io::ErrorKind::UnexpectedEof,
            "Packet too short for length header",
        ));
    }

    // Parse the 4-byte hex length prefix
    let length_str = std::str::from_utf8(&data[0..4])
        .map_err(|_| io::Error::new(io::ErrorKind::InvalidData, "Invalid length header"))?;

    let length = usize::from_str_radix(length_str, 16)
        .map_err(|_| io::Error::new(io::ErrorKind::InvalidData, "Invalid hex length"))?;

    // Length of 0 means flush packet
    if length == 0 {
        return Ok((4, &[]));
    }

    // Validate length
    if length < 4 {
        return Err(io::Error::new(
            io::ErrorKind::InvalidData,
            "Invalid packet length",
        ));
    }

    if data.len() < length {
        return Err(io::Error::new(
            io::ErrorKind::UnexpectedEof,
            "Packet data shorter than declared length",
        ));
    }

    // Return the packet data (excluding the 4-byte length header)
    Ok((length, &data[4..length]))
}

pub fn write_empty_advertisement_buffer(service: &str) -> Vec<u8> {
    let mut buf = Vec::new();

    let service_line = format!("# service={}\n", service);
    write_packet_line(&mut buf, service_line.as_bytes()).unwrap();

    write_flush_packet(&mut buf).unwrap();
    write_flush_packet(&mut buf).unwrap();

    hexdump(&buf);
    buf
}

/// Generate empty advertisement buffer for invalid services or missing repositories
pub fn generate_empty_advertisement_buffer(service: &str) -> Result<Vec<u8>, AppError> {
    let mut buf = Vec::new();

    // Write service announcement
    let service_line = format!("# service={}\n", service);
    write_packet_line(&mut buf, service_line.as_bytes()).map_err(|e| {
        AppError::GitAdvertisement {
            public: "Failed to generate empty advertisement".to_string(),
            internal: Some(format!("Failed to write service line: {}", e)),
        }
    })?;

    write_flush_packet(&mut buf).map_err(|e| AppError::GitAdvertisement {
        public: "Failed to generate empty advertisement".to_string(),
        internal: Some(format!("Failed to write flush packet: {}", e)),
    })?;

    write_flush_packet(&mut buf).map_err(|e| AppError::GitAdvertisement {
        public: "Failed to generate empty advertisement".to_string(),
        internal: Some(format!("Failed to write final flush packet: {}", e)),
    })?;

    Ok(buf)
}
