use std::path::PathBuf;
use tokio::fs;
use uuid::Uuid;

pub struct TempDir {
    pub path: PathBuf,
}

impl TempDir {
    pub async fn new(prefix: &str) -> Result<Self, std::io::Error> {
        let dir_id = Uuid::new_v4().to_string();
        let mut temp_path = std::env::temp_dir();
        temp_path.push(format!("{}_{}", prefix, dir_id));

        fs::create_dir(&temp_path).await?;

        Ok(Self { path: temp_path })
    }
}

// impl Drop for TempDir {
//     fn drop(&mut self) {
//         // Attempt to remove the temporary directory when the TempDir instance is dropped
//         if let Err(e) = std::fs::remove_dir_all(&self.path) {
//             eprintln!("Failed to remove temporary directory: {}", e);
//         }
//     }
// }
