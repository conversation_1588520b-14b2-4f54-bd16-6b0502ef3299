use async_trait::async_trait;
use std::sync::Arc;

use crate::{
    domain::git_advertisement_generator::{
        GitV1AdvertisementGenerator, GitV2AdvertisementGenerator,
    },
    error::AppError,
    types::{git::service::GitServiceType, repository::RepoIdentifier},
};

#[async_trait]
pub trait GitAdvertisementService: Send + Sync {
    async fn advertise_v1(
        &self,
        repo: &RepoIdentifier,
        service: &GitServiceType,
    ) -> Result<Vec<u8>, AppError>;

    async fn advertise_v2(&self, service: GitServiceType) -> Result<Vec<u8>, AppError>;
}

pub struct GitAdvertisementServiceImpl {
    v1_generator: Arc<dyn GitV1AdvertisementGenerator>,
    v2_generator: Arc<dyn GitV2AdvertisementGenerator>,
}

impl GitAdvertisementServiceImpl {
    pub fn new(
        v1_generator: Arc<dyn GitV1AdvertisementGenerator>,
        v2_generator: Arc<dyn GitV2AdvertisementGenerator>,
    ) -> Self {
        Self {
            v1_generator,
            v2_generator,
        }
    }
}

#[async_trait]
impl GitAdvertisementService for GitAdvertisementServiceImpl {
    async fn advertise_v1(
        &self,
        repo: &RepoIdentifier,
        service: &GitServiceType,
    ) -> Result<Vec<u8>, AppError> {
        match service {
            GitServiceType::UploadPack => {
                self.v1_generator
                    .generate_upload_pack_advertisement(repo)
                    .await
            }
            GitServiceType::ReceivePack => {
                self.v1_generator
                    .generate_receive_pack_advertisement(repo)
                    .await
            }
        }
    }

    async fn advertise_v2(&self, service: GitServiceType) -> Result<Vec<u8>, AppError> {
        match service {
            GitServiceType::UploadPack => {
                self.v2_generator.generate_upload_pack_advertisement().await
            }
            GitServiceType::ReceivePack => {
                self.v2_generator
                    .generate_receive_pack_advertisement()
                    .await
            }
        }
    }
}
