use async_trait::async_trait;

use crate::{error::AppError, types::repository::RepoIdentifier};

#[async_trait]
pub trait GitV1AdvertisementGenerator: Send + Sync {
    async fn generate_upload_pack_advertisement(
        &self,
        repo: &RepoIdentifier,
    ) -> Result<Vec<u8>, AppError>;

    async fn generate_receive_pack_advertisement(
        &self,
        repo: &RepoIdentifier,
    ) -> Result<Vec<u8>, AppError>;
}

#[async_trait]
pub trait GitV2AdvertisementGenerator: Send + Sync {
    async fn generate_upload_pack_advertisement(&self) -> Result<Vec<u8>, AppError>;

    async fn generate_receive_pack_advertisement(&self) -> Result<Vec<u8>, AppError>;
}
