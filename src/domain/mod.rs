use crate::{
    domain::{
        git_advertisement_generator::{GitV1AdvertisementGenerator, GitV2AdvertisementGenerator},
        services::git::advertisement::{GitAdvertisementService, GitAdvertisementServiceImpl},
    },
    infra::git::advertisement::{
        v1::GitV1AdvertisementGeneratorImpl, v2::GitV2AdvertisementGeneratorImpl,
    },
    types::git::config::{GitConfig, GitConfigImpl},
};
use std::sync::Arc;

pub mod git_advertisement_generator;
pub mod services;

pub struct DomainContext {
    git_v1_service: Arc<dyn GitAdvertisementService>,
    git_config: Arc<dyn GitConfig>,
    // git_advertisement_generator: Arc<dyn GitV1AdvertisementGenerator>,
}

impl Default for DomainContext {
    fn default() -> Self {
        Self::new()
    }
}

impl DomainContext {
    pub fn new() -> Self {
        // Try to create this once only
        let git_config = Arc::new(GitConfigImpl::init());

        let git_v1_advertisement_generator: Arc<dyn GitV1AdvertisementGenerator> =
            Arc::new(GitV1AdvertisementGeneratorImpl::new(git_config.clone()));

        let git_v2_advertisement_generator: Arc<dyn GitV2AdvertisementGenerator> =
            Arc::new(GitV2AdvertisementGeneratorImpl::new());

        Self {
            git_v1_service: Arc::new(GitAdvertisementServiceImpl::new(
                git_v1_advertisement_generator.clone(),
                git_v2_advertisement_generator.clone(),
            )),
            git_config,
            // git_advertisement_generator,
        }
    }

    pub fn get_git_config(&self) -> &Arc<dyn GitConfig + 'static> {
        &self.git_config
    }

    pub fn get_git_advertisement_service(&self) -> &Arc<dyn GitAdvertisementService + 'static> {
        &self.git_v1_service
    }
}
