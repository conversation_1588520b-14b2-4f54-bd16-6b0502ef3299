use axum::{body::Body, http::HeaderMap};
use tracing::Instrument;
use uuid::Uuid;

use axum::{
    http::{HeaderValue, Request},
    middleware::Next,
    response::Response,
};

use std::{str::FromStr, time::Instant};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>)]
pub struct ReqMetrics {
    start: Instant,
}

impl ReqMetrics {
    fn new() -> Self {
        Self {
            start: Instant::now(),
        }
    }
}

// TODO: fix unwraps in this function
async fn get_or_create_req_id(headers: &HeaderMap) -> String {
    match headers.get("x-request-id") {
        Some(header) => String::from_str(header.to_str().expect("just works")).expect("come onnnn"),
        None => Uuid::new_v4().to_string(),
    }
}

pub async fn set_req_id(mut req: Request<Body>, next: Next) -> Response {
    let req_id = get_or_create_req_id(req.headers()).await;

    // This should be ok since we are creating the header value from a uuid (valid UTF8)
    let req_id_header = HeaderValue::from_str(req_id.as_str()).expect("is valid header value");
    let res_req_id_header = req_id_header.clone();

    let start = ReqMetrics::new();

    req.headers_mut().insert("x-request-id", req_id_header);

    let uri = req.uri();

    tracing::info!("FIRST MW - url: {}", uri);
    req.extensions_mut().insert(start);

    let span = tracing::info_span!(
        "request",
        request_id = %req_id,
        method = %req.method(),
        uri = %req.uri(),
    );

    // Execute request within the span
    async move {
        let mut response = next.run(req).await;
        response
            .headers_mut()
            .insert("x-request-id", res_req_id_header);
        tracing::info!("Request completed");
        response
    }
    .instrument(span)
    .await
}

pub async fn measure_req(req: Request<Body>, next: Next) -> Response {
    let metrics = req.extensions().get::<ReqMetrics>().unwrap();

    let uri = req.uri().clone();
    tracing::info!(
        "Last mw - url: {} - elapsed: {:.2?}",
        uri,
        metrics.start.elapsed(),
    );

    let start = metrics.start;

    let response = next.run(req).await;

    tracing::info!("Request - time: {:.2?} - url: {}", start.elapsed(), uri);
    response
}
