use axum::{
    body::Bytes,
    extract::{Path as ExtractPath, State},
    http::{HeaderMap, StatusCode},
    response::IntoResponse,
};

use crate::{
    helpers::git_primitives::create_git_response_headers,
    internal::{
        command::run_command,
        git_receive_pack::{parse_receive_pack_request, process_receive_pack_request},
    },
    types::{
        git::{protocol::GitProtocol, service::GitServiceType},
        state::AppStateType,
    },
};

/// Handle Git receive-pack (push) requests
///
/// Note: This server does not automatically update HEAD to point to the first pushed branch.
/// Clients should ensure that the branch HEAD points to (typically 'main') is pushed first
/// to avoid inconsistent repository state. If feature branches are pushed before the HEAD
/// branch exists, the repository will have an unborn HEAD which may cause protocol errors
/// with some Git clients.
// #[axum::debug_handler]
pub async fn handle_git_receive_pack(
    State(state): AppStateType,
    headers: Header<PERSON>ap,
    ExtractPath((owner, repo)): ExtractPath<(String, String)>,
    body: Bytes,
) -> impl IntoResponse {
    let service = GitServiceType::ReceivePack;
    let protocol_version = headers
        .get("git-protocol")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");
    let protocol_version = GitProtocol::new(protocol_version);

    let mut response_headers =
        create_git_response_headers(&headers, &service.to_string(), &protocol_version).await;

    response_headers.insert(
        axum::http::header::CONTENT_TYPE,
        axum::http::HeaderValue::from_static("application/x-git-receive-pack-result"),
    );

    tracing::info!("Receive-pack request - Owner: {} - Repo: {}", owner, repo,);

    let repo_path = state.domain.get_git_config().get_repo_path(&owner, &repo);

    // Validate repository exists
    if !repo_path.exists() {
        tracing::info!("Repository does not exist: {}", repo_path.display());
        return (
            StatusCode::NOT_FOUND,
            [("Content-Type", "text/plain")],
            "Repository not found \n",
        )
            .into_response();
    }

    if state.domain.get_git_config().use_git_binary_fallback() {
        tracing::info!("Using git binary fallback for receive-pack (explicitly enabled)");
        handle_receive_pack_fallback(&repo_path, &body, response_headers).await
    } else {
        tracing::info!("Using native Git receive-pack protocol implementation");
        handle_receive_pack_native(&repo_path, &body, response_headers).await
    }
}

/// Handle receive-pack using native Rust implementation
async fn handle_receive_pack_native(
    repo_path: &std::path::Path,
    body: &axum::body::Bytes,
    response_headers: HeaderMap,
) -> axum::response::Response {
    // Parse the receive-pack request
    match parse_receive_pack_request(body) {
        Ok(request) => {
            tracing::info!(
                "Parsed receive-pack request: {} ref updates, {} capabilities",
                request.ref_updates.len(),
                request.capabilities.len()
            );

            // Process the request using our native implementation
            match process_receive_pack_request(repo_path, request).await {
                Ok(response_body) => {
                    (StatusCode::OK, response_headers, response_body).into_response()
                }
                Err(e) => {
                    tracing::error!("Native receive-pack processing failed: {:?}", e);
                    e.into_response()
                }
            }
        }
        Err(e) => {
            tracing::error!("Failed to parse receive-pack request: {:?}", e);
            e.into_response()
        }
    }
}

/// Handle receive-pack using git binary fallback (requires explicit opt-in)
async fn handle_receive_pack_fallback(
    repo_path: &std::path::Path,
    body: &axum::body::Bytes,
    response_headers: HeaderMap,
) -> axum::response::Response {
    let git_args = vec!["receive-pack", "--stateless-rpc", "."];

    // Convert Path to PathBuf for run_command
    let repo_pathbuf = repo_path.to_path_buf();
    match run_command("git", &git_args, Some(&repo_pathbuf), Some(body)).await {
        Ok(output) => {
            tracing::info!("Git receive-pack completed successfully");
            (StatusCode::OK, response_headers, output).into_response()
        }
        Err(e) => {
            tracing::error!("Git receive-pack failed: {:?}", e);
            e.into_response()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    #[test]
    fn test_protocol_version_parsing() {
        let mut headers = HeaderMap::new();
        headers.insert("Git-Protocol", HeaderValue::from_static("version=2"));

        let version = headers
            .get("git-protocol")
            .and_then(|h| h.to_str().ok())
            .and_then(|s| s.parse::<GitProtocol>().ok())
            .unwrap();
        assert_eq!(version, GitProtocol::V2);
    }

    #[test]
    fn test_protocol_version_default() {
        let headers = HeaderMap::new();
        let version = headers
            .get("git-protocol")
            .and_then(|h| h.to_str().ok())
            .and_then(|s| s.parse::<GitProtocol>().ok())
            .unwrap_or(GitProtocol::V1);
        assert_eq!(version, GitProtocol::V1);
    }
}
