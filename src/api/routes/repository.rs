use axum::{
    extract::State,
    http::StatusCode,
    response::{IntoResponse, Json},
};
use serde_json::json;
use tokio::fs;

use crate::types::state::AppStateType;
use serde::{Deserialize, Serialize};

#[derive(Deserialize)]
pub struct RepositoryRequest {
    pub owner: String,
    pub repo: String,
    pub default_branch: Option<String>,
    pub description: Option<String>,
}

#[derive(Serialize)]
pub struct RepoResponse {
    pub owner: String,
    pub repo: String,
    pub default_branch: String,
    pub clone_url: String,
}

pub async fn handle_create_repository(
    State(state): AppStateType,
    Json(repository_request): Json<RepositoryRequest>,
) -> impl IntoResponse {
    let owner = repository_request.owner;
    let repo = repository_request.repo;
    let default_branch = repository_request
        .default_branch
        .unwrap_or_else(|| "main".to_string());
    let description = repository_request.description;
    let repo_path = state
        .domain
        .get_git_config()
        .get_repo_path(&owner, &format!("{}.git", repo));

    let clone_url = format!(
        "{}/{}/{}.git",
        state.app_config.get_full_host(),
        owner,
        repo
    );

    if repo_path.exists() {
        tracing::info!("Repository already exists");
        return (
            StatusCode::BAD_REQUEST,
            Json(json!({ "message": "Repository already exists" })),
        )
            .into_response();
    }

    match fs::create_dir_all(&repo_path).await {
        Ok(_) => tracing::info!("Repository directory created successfully"),
        Err(e) => {
            tracing::error!("Failed to create repository directory: {}", e);
            return (
                StatusCode::INTERNAL_SERVER_ERROR,
                "Failed to create repository directory",
            )
                .into_response();
        }
    }

    match gix::init_bare(&repo_path) {
        Ok(_gix_repo) => {
            tracing::info!("Repository created successfully");

            // Set HEAD to point to the default branch (which doesn't exist yet - "unborn HEAD")
            // Clients should push to this branch first to establish it
            let head_ref = format!("ref: refs/heads/{}\n", default_branch);
            if let Err(e) = fs::write(repo_path.join("HEAD"), head_ref).await {
                tracing::error!("Failed to set HEAD reference to default branch: {}", e);
                return (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Failed to set HEAD reference to default branch",
                )
                    .into_response();
            }

            if let Some(description) = description {
                if let Err(e) = fs::write(repo_path.join("description"), description).await {
                    tracing::warn!("Failed to write repository description: {}", e);
                }
            }

            (
                StatusCode::CREATED,
                Json(RepoResponse {
                    owner,
                    repo,
                    default_branch,
                    clone_url,
                }),
            )
                .into_response()
        }
        Err(e) => {
            tracing::error!("Failed to create repository: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                "Failed to create repository",
            )
                .into_response()
        }
    }
}
