use axum::{
    extract::{Path, Query, State},
    http::{HeaderMap, StatusCode},
    response::IntoResponse,
};
use std::{collections::HashMap, str::FromStr};

use crate::{
    error::AppError,
    helpers::{
        git_pkt::generate_empty_advertisement_buffer, git_primitives::create_git_response_headers,
    },
    types::{
        git::{protocol::GitProtocol, service::GitServiceType},
        repository::RepoIdentifier,
        state::AppStateType,
    },
};

pub async fn handle_git_advertisement(
    State(state): AppStateType,
    headers: HeaderMap,
    Path((owner, repo)): Path<(String, String)>,
    Query(params): Query<HashMap<String, String>>,
) -> Result<impl IntoResponse, AppError> {
    let service = params.get("service").cloned().unwrap_or_default();
    let protocol_version = headers
        .get("git-protocol")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");
    let protocol_version = GitProtocol::new(protocol_version);

    let res_headers = create_git_response_headers(&headers, &service, &protocol_version).await;

    let service: GitServiceType = match GitServiceType::from_str(&service) {
        Ok(result) => result,
        Err(_) => {
            tracing::info!("Invalid advertisement service provided: {}", &service);

            let buf = generate_empty_advertisement_buffer(&service)?;
            return Ok((StatusCode::OK, res_headers, buf).into_response());
        }
    };

    // TEMP CLONE
    let repo = RepoIdentifier::new(owner.clone(), repo.clone());

    tracing::info!(
        "Git Protocol and service used: {} - {}",
        protocol_version,
        service
    );

    tracing::info!("Full request: headers={:?}", headers);
    tracing::info!("Advertisement request - owner: {}, repo: {:?}", owner, repo,);

    let repo_path = state
        .domain
        .get_git_config()
        .get_repo_path(&repo.owner, &repo.repo);

    if !repo_path.exists() {
        tracing::info!("Repository does not exist: {:?}", repo_path);
        return Ok((
            StatusCode::NOT_FOUND,
            [("Content-Type", "text/plain")],
            "Repository not found\n",
        )
            .into_response());
    }

    let body: Vec<u8> = match protocol_version {
        GitProtocol::V1 => {
            state
                .domain
                .get_git_advertisement_service()
                .advertise_v1(&repo, &service) // Temp clone
                .await?
        }
        GitProtocol::V2 => {
            state
                .domain
                .get_git_advertisement_service()
                .advertise_v2(service.clone())
                .await?
        }
    };

    Ok((StatusCode::OK, res_headers, body).into_response())
}
