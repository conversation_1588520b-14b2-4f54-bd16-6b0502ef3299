use axum::{
    body::{Body, Bytes},
    extract::{Path as ExtractPath, State},
    http::{HeaderMap, HeaderValue, Response, StatusCode, header},
    response::IntoResponse,
};
use tokio::{
    io::{AsyncReadExt, AsyncWriteExt},
    process::Command,
};

use crate::{
    helpers::{
        git_pkt::{write_flush_packet, write_packet_line},
        git_primitives::create_git_response_headers,
    },
    // internal::command::run_command,
    types::{
        git::{protocol::GitProtocol, service::GitServiceType},
        state::AppStateType,
    },
};
use std::path::Path;

pub async fn handle_git_upload_pack(
    State(state): AppStateType,
    headers: HeaderMap,
    ExtractPath((owner, repo)): ExtractPath<(String, String)>,
    body: axum::body::Bytes,
) -> impl IntoResponse {
    let service = GitServiceType::UploadPack;
    let protocol_version = headers
        .get("git-protocol")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");
    let protocol_version = GitProtocol::new(protocol_version);

    let mut response_headers =
        create_git_response_headers(&headers, &service.to_string(), &protocol_version).await;

    response_headers.insert(
        header::CONTENT_TYPE,
        HeaderValue::from_static("application/x-git-upload-pack-result"),
    );

    tracing::info!(
        "Upload-pack request - Owner: {} - Repo: {}, protocol: {}",
        owner,
        repo,
        protocol_version
    );

    let repo_path = state.domain.get_git_config().get_repo_path(&owner, &repo);

    if !repo_path.exists() {
        tracing::info!("Repository does not exist: {}", repo_path.display());
        return (
            StatusCode::NOT_FOUND,
            [("Content-Type", "text/plain")],
            "Repository not found\n",
        )
            .into_response();
    }

    if state.domain.get_git_config().use_git_binary_fallback() {
        tracing::info!("Using git binary fallback for upload-pack (explicitly enabled)");
        handle_upload_pack_fallback(&repo_path, &body, response_headers).await
    } else if protocol_version == GitProtocol::V2 {
        tracing::info!("Using native Git upload-pack protocol v2 implementation");
        handle_upload_pack_native_v2(&repo_path, &body, response_headers).await
    } else {
        tracing::info!("Using native Git upload-pack protocol v1 implementation");
        // handle_upload_pack_native_v1(&repo_path, &body, response_headers).await

        handle_upload_pack_fallback(&repo_path, &body, response_headers).await
    }
}

/// Handle upload-pack using git binary fallback (requires explicit opt-in)
async fn handle_upload_pack_fallback(
    repo_path: &std::path::Path,
    body: &axum::body::Bytes,
    response_headers: HeaderMap,
) -> axum::response::Response {
    // Check if this is a v2 protocol request by looking at the body
    let is_v2_request = String::from_utf8_lossy(body).contains("command=");

    let mut cmd = Command::new("git");
    cmd.arg("upload-pack")
        .arg("--stateless-rpc")
        .arg(".")
        .current_dir(repo_path)
        .stdin(std::process::Stdio::piped())
        .stdout(std::process::Stdio::piped())
        .stderr(std::process::Stdio::piped());

    // Set protocol version environment variable if this is a v2 request
    if is_v2_request {
        tracing::info!("Using git binary fallback with protocol v2");
        cmd.env("GIT_PROTOCOL", "version=2");
    } else {
        tracing::info!("Using git binary fallback with protocol v1");
    }

    let mut child = match cmd.spawn() {
        Ok(child) => child,
        Err(e) => {
            tracing::error!("Failed to spawn git upload-pack: {}", e);
            return (
                StatusCode::INTERNAL_SERVER_ERROR,
                format!("Failed to spawn git: {}", e),
            )
                .into_response();
        }
    };

    // Write request body to stdin
    if let Some(stdin) = child.stdin.take() {
        let mut stdin = stdin;
        // println!("WRITE BODY: {:?}", body.clone());
        if let Err(e) = stdin.write_all(body).await {
            tracing::error!("Failed to write to git stdin: {}", e);
            return (
                StatusCode::INTERNAL_SERVER_ERROR,
                format!("Failed to write to git stdin: {}", e),
            )
                .into_response();
        }
    }

    if let Some(stdout) = child.stdout.take() {
        let mut reader = stdout;

        let mut initial_buffer = [0u8; 1024];
        let initial_read = tokio::time::timeout(
            tokio::time::Duration::from_millis(5000),
            reader.read(&mut initial_buffer),
        )
        .await;

        match initial_read {
            Ok(Ok(0)) => {
                if let Ok(status) = child.wait().await {
                    if status.success() {
                        println!("CHILD succeeded");
                        let mut res = Response::builder().body(Body::empty()).expect("asd");
                        *res.headers_mut() = response_headers;
                        return res;
                    } else {
                        println!("STDERR: {:?}", child.stderr);
                        return (StatusCode::INTERNAL_SERVER_ERROR, "command failed")
                            .into_response();
                    }
                } else {
                    println!("No status");
                    return (StatusCode::INTERNAL_SERVER_ERROR, "command failed").into_response();
                }
            }
            Ok(Ok(n)) => {
                let stream = async_stream::stream! {
                    yield Ok(Bytes::copy_from_slice(&initial_buffer[..n]));
                    let mut buffer: [u8; 8192] = [0; 8192];
                    loop {
                        match reader.read(&mut buffer).await {
                            Ok(0) => break,
                            Ok(n) => {
                                yield Ok(Bytes::copy_from_slice(&buffer[..n]));
                            }
                            Err(e) => {
                                yield Err(e);
                                break;
                            }
                        }
                    }
                };
                let mut res = Response::builder()
                    .body(Body::from_stream(stream))
                    .expect("asd");

                let mut headers = response_headers;
                headers.insert(
                    header::TRANSFER_ENCODING,
                    HeaderValue::from_static("chunked"),
                );
                *res.headers_mut() = headers;

                return res;
            }
            _ => {
                println!("TIMED OUT");
                return (StatusCode::INTERNAL_SERVER_ERROR).into_response();
            }
        }
    }

    println!("DEFAULT ERROR RETURNED");
    (StatusCode::INTERNAL_SERVER_ERROR, "upload-pack failed").into_response()
}

/// Handle Git protocol v2 upload-pack requests using native implementation
async fn handle_upload_pack_native_v2(
    repo_path: &std::path::Path,
    body: &axum::body::Bytes,
    response_headers: HeaderMap,
) -> axum::response::Response {
    // Use our native v2 implementation
    match handle_v2_upload_pack_request(repo_path, body).await {
        Ok(response_body) => {
            tracing::info!("Native v2 upload-pack completed successfully");
            (StatusCode::OK, response_headers, response_body).into_response()
        }
        Err(e) => {
            tracing::error!("Native v2 upload-pack failed: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                format!("Upload-pack failed: {}", e),
            )
                .into_response()
        }
    }
}

/// Handle Git protocol v2 upload-pack requests (internal implementation)
async fn handle_v2_upload_pack_request(
    repo_path: &Path,
    request_body: &[u8],
) -> Result<Vec<u8>, String> {
    // Parse the v2 request to determine the command
    let request_str = String::from_utf8_lossy(request_body);

    if request_str.contains("command=ls-refs") {
        handle_ls_refs_command(repo_path, request_body).await
    } else if request_str.contains("command=fetch") {
        handle_fetch_command(repo_path, request_body).await
    } else {
        Err("Unknown v2 command".to_string())
    }
}

/// Handle ls-refs command for Git protocol v2
///
/// The ls-refs command lists references in the repository.
/// It supports various options:
/// - `symrefs`: Include symbolic reference targets
/// - `peel`: Include peeled tag information
/// - `unborn`: Include unborn HEAD information
async fn handle_ls_refs_command(repo_path: &Path, request_body: &[u8]) -> Result<Vec<u8>, String> {
    let repo = gix::open(repo_path).map_err(|e| format!("Failed to open repo: {}", e))?;
    let mut buf = Vec::new();

    // Parse ls-refs options from request
    let request_str = String::from_utf8_lossy(request_body);
    let show_symrefs = request_str.contains("symrefs");
    let show_peeled = request_str.contains("peel");

    // Get all refs
    let platform = repo
        .refs
        .iter()
        .map_err(|e| format!("Failed to get refs: {}", e))?;
    let refs: Vec<_> = platform
        .all()
        .map_err(|e| format!("Failed to collect refs: {}", e))?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| format!("Failed to iterate refs: {}", e))?;

    if refs.is_empty() {
        // Empty repo - advertise unborn HEAD if requested
        if request_str.contains("unborn") {
            let line = "unborn HEAD symref-target:refs/heads/main\n";
            write_packet_line(&mut buf, line.as_bytes())
                .map_err(|e| format!("Failed to write packet: {}", e))?;
        }
    } else {
        // First, handle HEAD explicitly if it exists
        if let Ok(head_ref) = repo.find_reference("HEAD") {
            if let Ok(peeled) = head_ref.clone().into_fully_peeled_id() {
                let mut line = format!("{} HEAD", peeled);

                // Add symref info if requested
                if show_symrefs {
                    if let Some(target_name) = head_ref.target().try_name() {
                        line.push_str(&format!(" symref-target:{}", target_name.as_bstr()));
                    }
                }

                line.push('\n');
                write_packet_line(&mut buf, line.as_bytes())
                    .map_err(|e| format!("Failed to write packet: {}", e))?;
            }
        }

        // Send other refs in v2 format
        for reference in refs {
            let name = reference.name.as_bstr();

            // Skip HEAD since we handled it above
            if name == "HEAD" {
                continue;
            }

            let oid_str = match reference.target {
                gix::refs::Target::Object(oid) => oid.to_string(),
                gix::refs::Target::Symbolic(_) => {
                    // For symbolic refs, resolve to actual commit
                    if let Ok(resolved) = repo.find_reference(name) {
                        if let Ok(peeled) = resolved.into_fully_peeled_id() {
                            peeled.to_string()
                        } else {
                            continue; // Skip unresolvable refs
                        }
                    } else {
                        continue; // Skip unresolvable refs
                    }
                }
            };

            let mut line = format!("{} {}", oid_str, name);

            // Add peeled info if requested and it's a tag
            if show_peeled && name.starts_with("refs/tags/".as_bytes()) {
                // TODO: Add peeled tag handling if needed
            }

            line.push('\n');
            write_packet_line(&mut buf, line.as_bytes())
                .map_err(|e| format!("Failed to write packet: {}", e))?;
        }
    }

    write_flush_packet(&mut buf).map_err(|e| format!("Failed to write flush packet: {}", e))?;
    Ok(buf)
}

/// Handle fetch command for Git protocol v2
///
/// The fetch command transfers objects from the repository.
/// For complex fetch operations, we delegate to the git binary.
async fn handle_fetch_command(repo_path: &Path, request_body: &[u8]) -> Result<Vec<u8>, String> {
    // For fetch commands, we delegate to git upload-pack with proper environment
    let mut cmd = std::process::Command::new("git");
    cmd.arg("-c")
        .arg("uploadpack.allowFilter=true")
        .arg("upload-pack")
        .arg("--stateless-rpc")
        .arg(".")
        .current_dir(repo_path)
        .env("GIT_PROTOCOL", "version=2")
        .stdin(std::process::Stdio::piped())
        .stdout(std::process::Stdio::piped())
        .stderr(std::process::Stdio::piped());

    let mut child = cmd
        .spawn()
        .map_err(|e| format!("Failed to spawn git: {}", e))?;

    // Write request body to stdin
    if let Some(stdin) = child.stdin.take() {
        use std::io::Write;
        let mut stdin = stdin;
        stdin
            .write_all(request_body)
            .map_err(|e| format!("Failed to write to git stdin: {}", e))?;
    }

    let output = child
        .wait_with_output()
        .map_err(|e| format!("Failed to wait for git: {}", e))?;

    if !output.status.success() {
        return Err(format!(
            "Git command failed: {}",
            String::from_utf8_lossy(&output.stderr)
        ));
    }

    Ok(output.stdout)
}

#[cfg(test)]
mod tests {
    use crate::types::git::protocol::GitProtocol;
    use std::str::FromStr;

    #[test]
    fn test_protocol_version_parsing() {
        assert_eq!(GitProtocol::from_str("version=2").unwrap(), GitProtocol::V2);
        assert_eq!(GitProtocol::from_str("version=1").unwrap(), GitProtocol::V1);
        assert_eq!(GitProtocol::from_str("").unwrap(), GitProtocol::V1);
    }

    #[test]
    fn test_protocol_version_default() {
        assert_eq!(GitProtocol::from_str("invalid").unwrap(), GitProtocol::V1);
    }
}
