use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};

// use std::error::Error;

use serde::Deserialize;
use serde_json::json;
use std::fmt;

#[derive(Debug, Deserialize)]
pub enum AppError {
    DatabaseError {
        internal: String,
    },
    InternalServerError {
        internal: String,
    },
    NotFound {
        public: String,
        internal: Option<String>,
    },
    BadRequest {
        public: String,
        internal: Option<String>,
    },
    Unauthorized {
        public: String,
        internal: Option<String>,
    },
    JobTimeout,
    GitAdvertisement {
        public: String,
        internal: Option<String>,
    },
    RepositoryNotFound {
        public: String,
        internal: Option<String>,
    },
    NotImplemented {
        public: String,
        internal: Option<String>,
    },
}

impl From<String> for AppError {
    fn from(err: String) -> Self {
        AppError::InternalServerError { internal: err }
    }
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            AppError::NotFound { internal, .. } => {
                write!(
                    f,
                    "Not found: {}",
                    internal.as_deref().unwrap_or("Not found")
                )
            }
            AppError::InternalServerError { internal, .. } => {
                write!(f, "Internal server error: {}", internal)
            }
            AppError::DatabaseError { internal, .. } => {
                write!(f, "Database error: {}", internal)
            }
            AppError::BadRequest { internal, .. } => write!(
                f,
                "Bad request: {}",
                internal.as_deref().unwrap_or("Bad request")
            ),
            AppError::Unauthorized { internal, .. } => write!(
                f,
                "Unauthorized: {}",
                internal.as_deref().unwrap_or("Unauthorized")
            ),
            AppError::JobTimeout => write!(f, "Job timeout"),
            AppError::GitAdvertisement { internal, .. } => write!(
                f,
                "Git advertisement error: {}",
                internal.as_deref().unwrap_or("Git advertisement error")
            ),
            AppError::RepositoryNotFound { internal, .. } => write!(
                f,
                "Repository not found: {}",
                internal.as_deref().unwrap_or("Repository not found")
            ),
            AppError::NotImplemented { internal, .. } => write!(
                f,
                "Not implemented: {}",
                internal.as_deref().unwrap_or("Not implemented")
            ),
        }
    }
}

impl AppError {
    pub fn with_error(&self) {
        match self {
            Self::InternalServerError { internal, .. } => {
                tracing::error!("Internal error: {}", internal);
            }
            Self::DatabaseError { internal, .. } => {
                tracing::error!("Internal error: {}", internal);
            }
            Self::NotFound { internal, .. } => {
                if let Some(internal) = internal {
                    tracing::error!("Not found: {}", internal);
                }
            }
            Self::BadRequest { internal, .. } => {
                if let Some(internal) = internal {
                    tracing::error!("Bad request: {}", internal);
                }
            }
            Self::Unauthorized { internal, .. } => {
                if let Some(internal) = internal {
                    tracing::error!("Unauthorized: {}", internal);
                }
            }
            Self::JobTimeout => {
                tracing::error!("Job timeout");
            }
            Self::GitAdvertisement { internal, .. } => {
                if let Some(internal) = internal {
                    tracing::error!("Git advertisement error: {}", internal);
                }
            }
            Self::RepositoryNotFound { internal, .. } => {
                if let Some(internal) = internal {
                    tracing::error!("Repository not found: {}", internal);
                }
            }
            Self::NotImplemented { internal, .. } => {
                if let Some(internal) = internal {
                    tracing::error!("Not implemented: {}", internal);
                }
            }
        };
    }

    pub fn public_message(&self) -> String {
        match self {
            AppError::DatabaseError { .. } => "Internal server error".to_string(),
            AppError::InternalServerError { .. } => "Internal server error".to_string(),
            AppError::NotFound { public, .. } => public.clone(),
            AppError::BadRequest { public, .. } => public.clone(),
            AppError::Unauthorized { public, .. } => public.clone(),
            AppError::JobTimeout => "Job timeout".to_string(),
            AppError::GitAdvertisement { public, .. } => public.clone(),
            AppError::RepositoryNotFound { public, .. } => public.clone(),
            AppError::NotImplemented { public, .. } => public.clone(),
        }
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        self.with_error();
        let (status, error_message) = match self {
            AppError::InternalServerError { .. } => (
                StatusCode::INTERNAL_SERVER_ERROR,
                "Unexpected error".to_string(),
            ),
            AppError::DatabaseError { .. } => (
                StatusCode::INTERNAL_SERVER_ERROR,
                "Unexpected error".to_string(),
            ),
            AppError::NotFound { public, .. } => (StatusCode::NOT_FOUND, public),
            AppError::BadRequest { public, .. } => (StatusCode::BAD_REQUEST, public),
            AppError::Unauthorized { public, .. } => (StatusCode::UNAUTHORIZED, public),
            AppError::JobTimeout => (StatusCode::INTERNAL_SERVER_ERROR, "Job timeout".to_string()),
            AppError::GitAdvertisement { public, .. } => (StatusCode::BAD_REQUEST, public),
            AppError::RepositoryNotFound { public, .. } => (StatusCode::NOT_FOUND, public),
            AppError::NotImplemented { public, .. } => (StatusCode::NOT_IMPLEMENTED, public),
        };
        let body = Json(json!({"error": {
            "message": error_message,
            "status": status.as_u16()
        }}));

        (status, body).into_response()
    }
}

// Implement From for various error types
impl From<std::io::Error> for AppError {
    fn from(e: std::io::Error) -> Self {
        AppError::InternalServerError {
            internal: format!("filesystem error: {}", e),
        }
    }
}

impl From<serde_json::Error> for AppError {
    fn from(e: serde_json::Error) -> Self {
        AppError::InternalServerError {
            internal: format!("Json error: {}", e),
        }
    }
}
